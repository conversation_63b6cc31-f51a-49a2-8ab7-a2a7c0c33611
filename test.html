<!DOCTYPE html>
<html>
<head>
    <title>测试页面</title>
</head>
<body>
    <h1>测试页面</h1>
    <p>如果你能看到这个页面，说明浏览器工作正常。</p>
    <script>
        console.log('测试页面加载成功');
        
        // 测试基本JavaScript功能
        const testElement = document.getElementById('test');
        if (testElement) {
            testElement.textContent = '测试成功';
        }
        
        // 模拟主页面的错误检查
        setTimeout(() => {
            const heatmapChart = document.getElementById('heatmap-chart');
            console.log('heatmapChart:', heatmapChart);
        }, 1000);
    </script>
    <div id="test">等待测试...</div>
</body>
</html>
