<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目里程碑达成率统计与趋势预测系统 - AgieCharmilles CUT E350</title>

    <!-- CSS框架和库 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <!-- Three.js使用动态加载 -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.plot.ly/plotly-2.27.0.min.js"></script>

    <script>
        // 备用Three.js OrbitControls
        window.addEventListener('load', function() {
            if (typeof THREE !== 'undefined' && !THREE.OrbitControls) {
                THREE.OrbitControls = function(camera, domElement) {
                    this.camera = camera;
                    this.domElement = domElement;
                    this.enableDamping = true;
                    this.dampingFactor = 0.05;

                    let isMouseDown = false;
                    let mouseX = 0, mouseY = 0;
                    let targetRotationX = 0, targetRotationY = 0;
                    let rotationX = 0, rotationY = 0;

                    this.update = function() {
                        rotationX += (targetRotationX - rotationX) * this.dampingFactor;
                        rotationY += (targetRotationY - rotationY) * this.dampingFactor;
                    };

                    this.reset = function() {
                        this.camera.position.set(0, 5, 10);
                        this.camera.lookAt(0, 0, 0);
                        targetRotationX = 0;
                        targetRotationY = 0;
                    };

                    // 简单的鼠标控制
                    if (domElement) {
                        domElement.addEventListener('mousedown', (e) => {
                            isMouseDown = true;
                            mouseX = e.clientX;
                            mouseY = e.clientY;
                        });

                        domElement.addEventListener('mousemove', (e) => {
                            if (isMouseDown) {
                                const deltaX = e.clientX - mouseX;
                                const deltaY = e.clientY - mouseY;
                                targetRotationY += deltaX * 0.01;
                                targetRotationX += deltaY * 0.01;
                                mouseX = e.clientX;
                                mouseY = e.clientY;
                            }
                        });

                        domElement.addEventListener('mouseup', () => {
                            isMouseDown = false;
                        });
                    }
                };
            }
        });
    </script>

    <style>
        :root {
            --primary-color: #00ff88;
            --secondary-color: #0066cc;
            --accent-color: #ff6600;
            --bg-dark: #1a1a1a;
            --bg-darker: #0d1117;
            --text-light: #ffffff;
            --text-muted: #8b949e;
            --border-color: #30363d;
            --success-color: #238636;
            --warning-color: #f85149;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-dark) 100%);
            color: var(--text-light);
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 机器显示器风格容器 */
        .machine-display {
            width: 100vw;
            height: 100vh;
            aspect-ratio: 1.8 / 1;
            max-width: 1800px;
            max-height: 1000px;
            margin: 0 auto;
            background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
            border: 3px solid var(--border-color);
            border-radius: 15px;
            box-shadow:
                0 0 30px rgba(0, 255, 136, 0.3),
                inset 0 0 20px rgba(0, 0, 0, 0.5);
            position: relative;
            overflow: hidden;
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 60px;
            background: linear-gradient(90deg, var(--bg-darker), var(--bg-dark));
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-family: 'Orbitron', monospace;
        }

        .system-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .system-status {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            box-shadow: 0 0 10px currentColor;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 主内容区域 */
        .main-content {
            height: calc(100% - 60px);
            display: flex;
            flex-direction: column;
        }

        /* 标签页导航 */
        .nav-tabs-custom {
            background: var(--bg-dark);
            border-bottom: 2px solid var(--border-color);
            padding: 0 20px;
            display: flex;
            overflow-x: auto;
        }

        .nav-tab {
            padding: 15px 25px;
            background: transparent;
            border: none;
            color: var(--text-muted);
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-tab:hover {
            color: var(--text-light);
            background: rgba(255, 255, 255, 0.05);
        }

        .nav-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: rgba(0, 255, 136, 0.1);
        }

        /* 内容面板 */
        .content-panel {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
        }

        .tab-content {
            display: none;
            height: 100%;
            animation: fadeIn 0.3s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 卡片样式 */
        .dashboard-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 预测指标样式 */
        .prediction-metric {
            text-align: center;
            padding: 10px;
        }

        .metric-label {
            font-size: 0.8rem;
            color: var(--text-muted);
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            font-family: 'Orbitron', monospace;
        }

        /* 3D控制面板样式 */
        .control-item {
            margin-bottom: 15px;
        }

        .control-item label {
            font-size: 0.8rem;
            color: var(--text-muted);
            margin-bottom: 5px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .info-label {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .info-value {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--primary-color);
            font-family: 'Orbitron', monospace;
        }

        .interaction-help {
            font-size: 0.8rem;
        }

        .help-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-muted);
        }

        .help-item i {
            width: 16px;
            color: var(--primary-color);
        }

        /* 优化的卡片样式 */
        .metric-card {
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.2);
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .metric-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            color: var(--primary-color);
            opacity: 0.7;
        }

        .metric-icon.warning {
            color: var(--accent-color);
        }

        .metric-content {
            text-align: center;
            z-index: 2;
        }

        .metric-value {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--primary-color);
            font-family: 'Orbitron', monospace;
            line-height: 1;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-light);
            margin-bottom: 8px;
            font-weight: 500;
        }

        .metric-change {
            font-size: 0.8rem;
            font-weight: 500;
        }

        .metric-change.positive {
            color: var(--success-color);
        }

        .metric-change.negative {
            color: var(--warning-color);
        }

        /* 图表卡片样式 */
        .chart-card {
            min-height: 350px;
        }

        .chart-container {
            height: 300px;
            width: 100%;
        }

        .flow-chart-container {
            height: 250px;
            width: 100%;
        }

        /* 信息卡片样式 */
        .info-card {
            height: 100%;
        }

        .status-list {
            height: 280px;
            overflow-y: auto;
            padding-right: 5px;
        }

        .status-list::-webkit-scrollbar {
            width: 4px;
        }

        .status-list::-webkit-scrollbar-track {
            background: var(--bg-dark);
        }

        .status-list::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 2px;
        }

        /* 统计网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            padding: 10px 0;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary-color);
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            font-family: 'Orbitron', monospace;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        /* 卡片操作按钮 */
        .card-actions {
            display: flex;
            gap: 5px;
        }

        .card-actions .btn {
            padding: 4px 8px;
            font-size: 0.8rem;
        }

        /* 里程碑页面样式 */
        .timeline-container {
            height: 180px;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 20px 0;
        }

        .milestone-list {
            height: 450px;
            overflow-y: auto;
            padding-right: 8px;
        }

        .milestone-list::-webkit-scrollbar {
            width: 6px;
        }

        .milestone-list::-webkit-scrollbar-track {
            background: var(--bg-dark);
            border-radius: 3px;
        }

        .milestone-list::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .upcoming-list {
            height: 280px;
            overflow-y: auto;
            padding-right: 5px;
        }

        .upcoming-list::-webkit-scrollbar {
            width: 4px;
        }

        .upcoming-list::-webkit-scrollbar-track {
            background: var(--bg-dark);
        }

        .upcoming-list::-webkit-scrollbar-thumb {
            background: var(--accent-color);
            border-radius: 2px;
        }

        /* AI控制面板样式 */
        .ai-control-panel {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(0, 102, 204, 0.1));
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .ai-metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .ai-metric {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .ai-metric:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .ai-metric-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 8px;
            font-size: 1.2rem;
            color: white;
        }

        .ai-metric-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--primary-color);
            font-family: 'Orbitron', monospace;
            line-height: 1;
        }

        .ai-metric-label {
            font-size: 0.85rem;
            color: #ffffff;
            opacity: 0.9;
            font-weight: 500;
            margin-top: 2px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        /* 预警列表样式 */
        .warning-list {
            height: 250px;
            overflow-y: auto;
            padding-right: 5px;
        }

        .warning-list::-webkit-scrollbar {
            width: 4px;
        }

        .warning-list::-webkit-scrollbar-track {
            background: var(--bg-dark);
        }

        .warning-list::-webkit-scrollbar-thumb {
            background: var(--warning-color);
            border-radius: 2px;
        }

        /* 简单图表替代样式 */
        .simple-chart {
            background: rgba(255, 255, 255, 0.02);
            border-radius: 8px;
            padding: 20px;
        }

        .metric-item {
            transition: all 0.3s ease;
        }

        .metric-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 255, 136, 0.2);
        }

        .heat-cell {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .heat-cell:hover {
            transform: scale(1.1);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        /* 工业控制台布局样式 */
        .control-console {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.05), rgba(0, 255, 136, 0.02));
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 8px;
            padding: 20px;
            position: relative;
        }

        .control-console::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00ff88, #00ccff, #00ff88);
            border-radius: 8px 8px 0 0;
        }

        /* KPI面板 */
        .kpi-panel {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(0, 255, 136, 0.15);
            border-radius: 6px;
            padding: 15px;
            height: 100%;
        }

        .panel-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            font-weight: 600;
            color: #00ff88;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: auto;
        }

        .status-indicator.online {
            background: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
            animation: pulse 2s infinite;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .kpi-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .kpi-item.primary {
            background: rgba(0, 255, 136, 0.1);
            border-left-color: #00ff88;
        }

        .kpi-item.secondary {
            background: rgba(0, 204, 255, 0.1);
            border-left-color: #00ccff;
        }

        .kpi-item.success {
            background: rgba(0, 255, 136, 0.1);
            border-left-color: #00ff88;
        }

        .kpi-item.warning {
            background: rgba(255, 102, 0, 0.1);
            border-left-color: #ff6600;
        }

        .kpi-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 255, 136, 0.2);
        }

        .kpi-icon {
            font-size: 1.5rem;
            opacity: 0.8;
        }

        .kpi-value {
            font-size: 1.8rem;
            font-weight: bold;
            font-family: 'Orbitron', monospace;
            line-height: 1;
        }

        .kpi-label {
            font-size: 0.8rem;
            opacity: 0.9;
            margin-top: 2px;
        }

        .kpi-trend {
            font-size: 0.7rem;
            font-weight: 600;
            margin-top: 4px;
        }

        .kpi-trend.up {
            color: #00ff88;
        }

        .kpi-trend.down {
            color: #ff6600;
        }

        /* 系统监控面板 */
        .system-monitor {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(0, 255, 136, 0.15);
            border-radius: 6px;
            padding: 15px;
            height: 100%;
        }

        .monitor-grid {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .monitor-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .monitor-label {
            font-size: 0.8rem;
            min-width: 80px;
            opacity: 0.9;
        }

        .monitor-bar {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
        }

        .bar-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        .monitor-value {
            font-size: 0.8rem;
            font-family: 'Orbitron', monospace;
            min-width: 40px;
            text-align: right;
        }

        /* 分析面板 */
        .analysis-section {
            margin-top: 20px;
        }

        .analysis-panel {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(0, 255, 136, 0.15);
            border-radius: 8px;
            padding: 20px;
            height: 100%;
        }

        .panel-controls {
            display: flex;
            gap: 8px;
            margin-left: auto;
        }

        .control-btn {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            color: #00ff88;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(0, 255, 136, 0.2);
            transform: translateY(-1px);
        }

        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        /* 分析图表网格 */
        .analysis-chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: calc(100vh - 280px); /* 向下拉伸至底边 */
            margin-top: 20px;
        }

        /* 图表面板 */
        .chart-panel {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
        }

        .chart-panel:hover {
            border-color: rgba(0, 255, 136, 0.4);
            box-shadow: 0 4px 20px rgba(0, 255, 136, 0.1);
        }

        .chart-panel-header {
            background: rgba(0, 255, 136, 0.1);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(0, 255, 136, 0.2);
        }

        .chart-title {
            color: #ffffff;
            font-weight: 600;
            font-size: 14px;
        }

        .chart-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff88;
            box-shadow: 0 0 8px rgba(0, 255, 136, 0.6);
            animation: pulse 2s infinite;
        }

        .chart-panel-content {
            flex: 1;
            padding: 0;
            position: relative;
            overflow: hidden;
        }

        /* 3D性能立体图样式 */
        .performance-cube-visualization {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 20px;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.05) 0%, rgba(0, 204, 255, 0.03) 50%, rgba(102, 51, 255, 0.02) 100%);
            perspective: 1000px;
        }

        .cube-title-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .cube-main-title {
            color: #ffffff;
            font-size: 18px;
            font-weight: 700;
            margin: 0 0 5px 0;
            text-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
            background: linear-gradient(45deg, #00ff88, #00ccff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .cube-subtitle {
            color: #8b949e;
            font-size: 12px;
            margin: 0;
            font-style: italic;
        }

        .cube-container {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            transform-style: preserve-3d;
        }

        .cube-framework {
            position: absolute;
            width: 300px;
            height: 300px;
            transform-style: preserve-3d;
            animation: cubeRotate 20s infinite linear;
        }

        .cube-face {
            position: absolute;
            width: 300px;
            height: 300px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            background: rgba(0, 255, 136, 0.02);
            backdrop-filter: blur(5px);
        }

        .cube-face.front { transform: translateZ(150px); }
        .cube-face.back { transform: translateZ(-150px) rotateY(180deg); }
        .cube-face.left { transform: rotateY(-90deg) translateZ(150px); }
        .cube-face.right { transform: rotateY(90deg) translateZ(150px); }
        .cube-face.top { transform: rotateX(90deg) translateZ(150px); }
        .cube-face.bottom { transform: rotateX(-90deg) translateZ(150px); }

        .face-grid {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .grid-line {
            position: absolute;
            background: rgba(0, 255, 136, 0.2);
        }

        .grid-line.horizontal {
            width: 100%;
            height: 1px;
            top: var(--position);
        }

        .grid-line.vertical {
            height: 100%;
            width: 1px;
            left: var(--position);
        }

        .performance-spheres {
            position: absolute;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
        }

        .perf-sphere {
            position: absolute;
            width: 60px;
            height: 60px;
            left: var(--x);
            top: var(--y);
            transform: translate(-50%, -50%) translateZ(calc(var(--z) * 1px - 150px)) scale(calc(var(--size) / 100));
            opacity: 0;
            transition: all 0.6s ease;
        }

        .perf-sphere.animate-in {
            opacity: 1;
        }

        .sphere-core {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, #00ff88, #00ccff, #0066ff);
            box-shadow:
                0 0 20px rgba(0, 255, 136, 0.8),
                inset 0 0 20px rgba(255, 255, 255, 0.2);
            animation: spherePulse 3s infinite;
        }

        .sphere-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            border: 1px solid rgba(0, 255, 136, 0.6);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: ringExpand 2s infinite;
        }

        .sphere-ring.ring-1 {
            width: 80px;
            height: 80px;
            animation-delay: 0s;
        }

        .sphere-ring.ring-2 {
            width: 100px;
            height: 100px;
            animation-delay: 0.7s;
        }

        .sphere-label {
            position: absolute;
            top: 120%;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 8px;
            padding: 6px 10px;
            backdrop-filter: blur(10px);
            color: #ffffff;
            font-size: 10px;
            font-weight: 600;
            white-space: nowrap;
        }

        .sphere-label span {
            display: block;
            color: #00ff88;
            font-size: 12px;
            font-weight: 700;
            margin-top: 2px;
        }

        .connection-lines {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .connect-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg,
                rgba(0, 255, 136, 0) 0%,
                rgba(0, 255, 136, 0.8) 50%,
                rgba(0, 204, 255, 0) 100%);
            transform-origin: left center;
            animation: lineFlow 3s infinite;
        }

        .center-stats-panel {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .stats-hologram {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff88;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            backdrop-filter: blur(15px);
            box-shadow:
                0 0 30px rgba(0, 255, 136, 0.5),
                inset 0 0 20px rgba(0, 255, 136, 0.1);
            animation: hologramFloat 4s infinite;
        }

        .hologram-value {
            color: #00ff88;
            font-size: 24px;
            font-weight: 700;
            text-shadow: 0 0 15px rgba(0, 255, 136, 1);
            margin-bottom: 5px;
        }

        .hologram-label {
            color: #ffffff;
            font-size: 11px;
            margin-bottom: 5px;
        }

        .hologram-trend {
            color: #00ccff;
            font-size: 12px;
            font-weight: 600;
        }

        @keyframes cubeRotate {
            0% { transform: rotateX(0deg) rotateY(0deg); }
            100% { transform: rotateX(360deg) rotateY(360deg); }
        }

        @keyframes spherePulse {
            0%, 100% { transform: scale(1); box-shadow: 0 0 20px rgba(0, 255, 136, 0.8); }
            50% { transform: scale(1.1); box-shadow: 0 0 30px rgba(0, 255, 136, 1); }
        }

        @keyframes ringExpand {
            0% { opacity: 1; transform: translate(-50%, -50%) scale(0.8); }
            100% { opacity: 0; transform: translate(-50%, -50%) scale(1.5); }
        }

        @keyframes lineFlow {
            0% { opacity: 0; transform: scaleX(0); }
            50% { opacity: 1; transform: scaleX(1); }
            100% { opacity: 0; transform: scaleX(0); }
        }

        @keyframes hologramFloat {
            0%, 100% { transform: translate(-50%, -50%) translateY(0px); }
            50% { transform: translate(-50%, -50%) translateY(-5px); }
        }

        /* 流动网络图样式 */
        .flow-network-visualization {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 20px;
            background:
                radial-gradient(circle at 20% 30%, rgba(0, 255, 136, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(0, 204, 255, 0.06) 0%, transparent 50%),
                linear-gradient(135deg, rgba(102, 51, 255, 0.03) 0%, rgba(0, 0, 0, 0.1) 100%);
            overflow: hidden;
        }

        .network-title-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .network-main-title {
            color: #ffffff;
            font-size: 18px;
            font-weight: 700;
            margin: 0 0 5px 0;
            text-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
            background: linear-gradient(45deg, #00ff88, #00ccff, #6633ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleShimmer 3s infinite;
        }

        .network-subtitle {
            color: #8b949e;
            font-size: 12px;
            margin: 0;
            font-style: italic;
        }

        .network-container {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .particle-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: var(--size);
            height: var(--size);
            background: radial-gradient(circle, rgba(0, 255, 136, 0.8) 0%, rgba(0, 204, 255, 0.4) 50%, transparent 100%);
            border-radius: 50%;
            left: var(--x);
            top: var(--y);
            animation: particleFloat var(--duration) infinite ease-in-out;
            animation-delay: var(--delay);
        }

        .network-nodes {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .network-node {
            position: absolute;
            left: var(--x);
            top: var(--y);
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: all 0.6s ease;
        }

        .network-node.animate-in {
            opacity: 1;
        }

        .core-node .node-core {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, #00ff88, #00ccff, #6633ff);
            box-shadow:
                0 0 40px rgba(0, 255, 136, 0.8),
                0 0 80px rgba(0, 204, 255, 0.4),
                inset 0 0 30px rgba(255, 255, 255, 0.2);
        }

        .core-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            background: #ffffff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: corePulse 2s infinite;
        }

        .core-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            border: 2px solid rgba(0, 255, 136, 0.6);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: coreRingExpand 3s infinite;
        }

        .core-ring.ring-1 {
            width: 100px;
            height: 100px;
            animation-delay: 0s;
        }

        .core-ring.ring-2 {
            width: 120px;
            height: 120px;
            animation-delay: 1s;
        }

        .core-ring.ring-3 {
            width: 140px;
            height: 140px;
            animation-delay: 2s;
        }

        .project-node .node-sphere {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            position: relative;
            background: radial-gradient(circle at 30% 30%, #00ccff, #0066ff, #003399);
            box-shadow: 0 0 25px rgba(0, 204, 255, 0.8);
            animation: projectPulse 4s infinite;
        }

        .alpha-sphere { background: radial-gradient(circle at 30% 30%, #ff6b6b, #ee5a52, #d63031); box-shadow: 0 0 25px rgba(255, 107, 107, 0.8); }
        .beta-sphere { background: radial-gradient(circle at 30% 30%, #4ecdc4, #00b894, #00a085); box-shadow: 0 0 25px rgba(78, 205, 196, 0.8); }
        .gamma-sphere { background: radial-gradient(circle at 30% 30%, #ffe66d, #fdcb6e, #e17055); box-shadow: 0 0 25px rgba(255, 230, 109, 0.8); }
        .delta-sphere { background: radial-gradient(circle at 30% 30%, #a29bfe, #6c5ce7, #5f3dc4); box-shadow: 0 0 25px rgba(162, 155, 254, 0.8); }

        .sphere-glow {
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border-radius: 50%;
            background: inherit;
            filter: blur(10px);
            opacity: 0.6;
            animation: glowPulse 3s infinite;
        }

        .data-flow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 6px;
            background: #ffffff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: dataFlowPulse 1.5s infinite;
        }

        .function-node {
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid rgba(0, 255, 136, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }

        .function-icon {
            font-size: 18px;
            filter: drop-shadow(0 0 5px rgba(0, 255, 136, 0.8));
        }

        .node-label {
            position: absolute;
            top: 120%;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 8px;
            padding: 6px 10px;
            backdrop-filter: blur(15px);
            color: #ffffff;
            font-size: 10px;
            font-weight: 600;
            white-space: nowrap;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
        }

        .label-title {
            display: block;
            margin-bottom: 2px;
        }

        .label-value {
            display: block;
            color: #00ff88;
            font-size: 12px;
            font-weight: 700;
        }

        .connection-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .flow-path {
            filter: drop-shadow(0 0 3px rgba(0, 255, 136, 0.8));
        }

        .flow-path.secondary {
            opacity: 0.7;
        }

        .flow-indicators {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .flow-indicator {
            position: absolute;
            left: var(--start-x);
            top: var(--start-y);
            animation: flowMove 3s infinite;
            animation-delay: var(--delay);
        }

        .indicator-dot {
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #00ff88, #00ccff);
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }

        .network-stats-panel {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid rgba(0, 255, 136, 0.3);
            border-radius: 15px;
            padding: 15px;
            backdrop-filter: blur(15px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
        }

        .hologram-title {
            color: #00ff88;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 10px;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            color: #00ccff;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 2px;
            text-shadow: 0 0 8px rgba(0, 204, 255, 0.8);
        }

        .stat-label {
            color: #8b949e;
            font-size: 9px;
        }

        /* 动画关键帧 */
        @keyframes titleShimmer {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes particleFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        @keyframes corePulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
        }

        @keyframes coreRingExpand {
            0% { opacity: 1; transform: translate(-50%, -50%) scale(0.8); }
            100% { opacity: 0; transform: translate(-50%, -50%) scale(1.5); }
        }

        @keyframes projectPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes glowPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @keyframes dataFlowPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.5; }
        }

        @keyframes flowMove {
            0% {
                left: var(--start-x);
                top: var(--start-y);
                opacity: 0;
            }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% {
                left: var(--end-x);
                top: var(--end-y);
                opacity: 0;
            }
        }

        .chart-cell {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(0, 255, 136, 0.1);
            border-radius: 6px;
            padding: 15px;
            min-height: 300px;
        }

        .chart-cell.primary {
            border-left: 4px solid #00ff88;
        }

        .chart-cell.secondary {
            border-left: 4px solid #00ccff;
        }

        .chart-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 0.9rem;
            font-weight: 600;
            color: #ffffff;
        }

        .chart-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .chart-status.active {
            background: #00ff88;
            box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
        }

        .chart-content {
            height: calc(100% - 40px);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 监控面板 */
        .monitoring-panel {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(0, 255, 136, 0.15);
            border-radius: 8px;
            padding: 20px;
            height: 100%;
        }

        .monitor-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-monitor {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(0, 255, 136, 0.1);
            border-radius: 4px;
            padding: 10px;
            max-height: 150px;
            overflow-y: auto;
        }

        .metrics-display {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 4px;
            border-left: 3px solid rgba(0, 255, 136, 0.3);
        }

        .metric-name {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .metric-value {
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .metric-value.success {
            color: #00ff88;
        }

        .metric-value.warning {
            color: #ffff00;
        }

        .metric-value.danger {
            color: #ff6600;
        }

        .alert-display {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .alert-item.warning {
            background: rgba(255, 102, 0, 0.1);
            border-left: 3px solid #ff6600;
            color: #ff6600;
        }

        .alert-item.info {
            background: rgba(0, 204, 255, 0.1);
            border-left: 3px solid #00ccff;
            color: #00ccff;
        }

        /* 流程控制面板 */
        .process-control {
            margin-top: 20px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(0, 255, 136, 0.15);
            border-radius: 8px;
            padding: 20px;
        }

        .flow-display {
            background: rgba(255, 255, 255, 0.01);
            border: 1px solid rgba(0, 255, 136, 0.1);
            border-radius: 6px;
            padding: 20px;
            margin-top: 15px;
            min-height: 300px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .kpi-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .chart-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .kpi-item {
                padding: 12px;
            }
        }

        @media (max-width: 768px) {
            .control-console {
                padding: 15px;
            }

            .panel-header {
                font-size: 0.8rem;
                flex-wrap: wrap;
            }

            .panel-controls {
                margin-left: 0;
                margin-top: 10px;
            }

            .kpi-value {
                font-size: 1.5rem;
            }

            .monitor-label {
                min-width: 60px;
                font-size: 0.7rem;
            }

            .analysis-panel,
            .monitoring-panel,
            .control-panel {
                padding: 15px;
            }

            .analysis-chart-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                height: auto;
            }

            .chart-panel-header {
                padding: 10px 12px;
            }

            .chart-title {
                font-size: 12px;
            }

            /* 3D立体图响应式 */
            .cube-framework {
                width: 250px;
                height: 250px;
            }

            .cube-face {
                width: 250px;
                height: 250px;
            }

            .perf-sphere {
                width: 45px;
                height: 45px;
            }

            .sphere-label {
                font-size: 9px;
                padding: 4px 6px;
            }

            .stats-hologram {
                padding: 10px;
            }

            .hologram-value {
                font-size: 18px;
            }

            /* 流动网络图响应式 */
            .core-node .node-core {
                width: 60px;
                height: 60px;
            }

            .core-ring.ring-1 { width: 80px; height: 80px; }
            .core-ring.ring-2 { width: 100px; height: 100px; }
            .core-ring.ring-3 { width: 120px; height: 120px; }

            .project-node .node-sphere {
                width: 40px;
                height: 40px;
            }

            .function-node {
                width: 35px;
                height: 35px;
            }

            .function-icon {
                font-size: 14px;
            }

            .network-stats-panel {
                bottom: 10px;
                right: 10px;
                padding: 10px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 6px;
            }

            .stat-value {
                font-size: 12px;
            }

            .project-name {
                font-size: 11px;
            }

            .project-avg {
                font-size: 9px;
            }

            .data-cells {
                gap: 3px;
            }

            .advanced-heat-cell {
                padding: 10px 4px;
            }

            .cell-value {
                font-size: 10px;
            }

            .heatmap-stats-panel {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
                padding: 10px;
            }

            .stats-value {
                font-size: 14px;
            }

            .legend-items {
                grid-template-columns: 1fr;
                gap: 6px;
            }
        }

        @media (max-width: 576px) {
            .kpi-item {
                flex-direction: column;
                text-align: center;
                gap: 8px;
            }

            .monitor-item {
                flex-direction: column;
                gap: 5px;
            }

            .monitor-bar {
                width: 100%;
            }

            .metric-row {
                padding: 6px 8px;
            }
        }

        /* 项目状态列表样式 */
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 4px;
            border-left: 3px solid rgba(0, 255, 136, 0.3);
            transition: all 0.3s ease;
        }

        .status-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateX(5px);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .status-dot.success {
            background: #00ff88;
            box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
        }

        .status-dot.warning {
            background: #ffff00;
            box-shadow: 0 0 8px rgba(255, 255, 0, 0.5);
        }

        .status-dot.danger {
            background: #ff6600;
            box-shadow: 0 0 8px rgba(255, 102, 0, 0.5);
        }

        .status-info {
            flex: 1;
        }

        .status-name {
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .status-progress {
            font-size: 0.7rem;
            opacity: 0.8;
            font-family: 'Orbitron', monospace;
        }

        /* 通知样式 */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .notification {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
        }

        /* 工业图表容器样式 */
        .industrial-chart-container {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(0, 255, 136, 0.15);
            border-radius: 8px;
            padding: 15px;
            height: 100%;
            position: relative;
        }

        .chart-status-bar {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 255, 136, 0.1);
        }

        .chart-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #00ff88;
            flex: 1;
        }

        .chart-mode {
            font-size: 0.7rem;
            color: #ff6600;
            background: rgba(255, 102, 0, 0.1);
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid rgba(255, 102, 0, 0.3);
        }

        /* 雷达图模拟样式 - 重新设计 */
        .radar-simulation {
            position: relative;
            width: 100%;
            height: 350px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: radial-gradient(circle, rgba(0, 255, 136, 0.05) 0%, rgba(0, 0, 0, 0.2) 100%);
            border-radius: 8px;
        }

        .radar-container {
            position: relative;
            width: 300px;
            height: 300px;
        }

        .radar-grid {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
        }

        .radar-circle {
            position: absolute;
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .radar-circle:nth-child(1) { width: 60px; height: 60px; }
        .radar-circle:nth-child(2) { width: 120px; height: 120px; }
        .radar-circle:nth-child(3) { width: 180px; height: 180px; }
        .radar-circle:nth-child(4) { width: 240px; height: 240px; }
        .radar-circle:nth-child(5) { width: 300px; height: 300px; border-color: rgba(0, 255, 136, 0.4); }

        .radar-axis {
            position: absolute;
            width: 1px;
            height: 150px;
            background: rgba(0, 255, 136, 0.3);
            top: 0;
            left: 50%;
            transform-origin: bottom center;
        }

        .radar-axis:nth-child(6) { transform: translateX(-50%) rotate(0deg); }
        .radar-axis:nth-child(7) { transform: translateX(-50%) rotate(60deg); }
        .radar-axis:nth-child(8) { transform: translateX(-50%) rotate(120deg); }
        .radar-axis:nth-child(9) { transform: translateX(-50%) rotate(180deg); }
        .radar-axis:nth-child(10) { transform: translateX(-50%) rotate(240deg); }
        .radar-axis:nth-child(11) { transform: translateX(-50%) rotate(300deg); }

        .radar-center {
            position: absolute;
            width: 100px;
            height: 100px;
            border: 3px solid #00ff88;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(0, 255, 136, 0.15);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }

        .center-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00ff88;
            font-family: 'Orbitron', monospace;
            line-height: 1;
        }

        .center-label {
            font-size: 0.8rem;
            color: #ffffff;
            opacity: 0.9;
            margin-top: 4px;
        }

        .radar-metrics {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
        }

        .metric-sector {
            position: absolute;
            top: 50%;
            left: 50%;
            transform-origin: 0 0;
        }

        .metric-point {
            position: absolute;
            width: 16px;
            height: 16px;
            background: #00ff88;
            border: 2px solid #ffffff;
            border-radius: 50%;
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
            animation: pulse 2s infinite;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .metric-point:hover {
            transform: scale(1.3);
            box-shadow: 0 0 25px rgba(0, 255, 136, 0.8);
        }

        .metric-label {
            position: absolute;
            font-size: 0.85rem;
            text-align: center;
            color: #ffffff;
            font-weight: 600;
            background: rgba(0, 0, 0, 0.7);
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            white-space: nowrap;
            z-index: 5;
        }

        .metric-value {
            color: #00ff88;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            display: block;
            margin-top: 2px;
        }

        /* 热力图模拟样式 */
        .heatmap-simulation {
            width: 100%;
            height: 250px;
        }

        .heatmap-header {
            margin-bottom: 10px;
        }

        .time-axis {
            display: grid;
            grid-template-columns: 80px repeat(5, 1fr);
            gap: 8px;
            font-size: 0.8rem;
            color: #8b949e;
            text-align: center;
        }

        .heatmap-body {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .project-row {
            display: grid;
            grid-template-columns: 80px repeat(5, 1fr);
            gap: 8px;
            align-items: center;
        }

        .project-label {
            font-size: 0.8rem;
            color: #8b949e;
            text-align: right;
            padding-right: 8px;
        }

        .heat-cell {
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-size: 0.8rem;
            font-weight: bold;
            font-family: 'Orbitron', monospace;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .heat-cell.high {
            background: #00ff88;
            color: #000000;
        }

        .heat-cell.medium {
            background: #ffff00;
            color: #000000;
        }

        .heat-cell.low {
            background: #ff6600;
            color: #ffffff;
        }

        .heat-cell:hover {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
        }

        .heatmap-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            font-size: 0.7rem;
            color: #8b949e;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .legend-color.high {
            background: #00ff88;
        }

        .legend-color.medium {
            background: #ffff00;
        }

        .legend-color.low {
            background: #ff6600;
        }

        /* 通知样式增强 */
        .notification-content {
            line-height: 1.4;
        }

        .status-section {
            margin-bottom: 8px;
        }

        .status-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .status-list {
            opacity: 0.9;
            font-size: 0.85rem;
        }

        /* 3D替代方案样式 */
        .three-fallback-container {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(0, 255, 136, 0.15);
            border-radius: 8px;
            padding: 20px;
            height: 100%;
            min-height: 400px;
        }

        .fallback-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 255, 136, 0.1);
        }

        .status-indicator.offline {
            background: #ff6600;
            box-shadow: 0 0 10px rgba(255, 102, 0, 0.5);
        }

        .fallback-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #00ff88;
            flex: 1;
        }

        .fallback-mode {
            font-size: 0.7rem;
            color: #ff6600;
            background: rgba(255, 102, 0, 0.1);
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid rgba(255, 102, 0, 0.3);
        }

        .pseudo-3d-scene {
            position: relative;
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.05), rgba(0, 0, 0, 0.3));
            border: 1px solid rgba(0, 255, 136, 0.1);
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .scene-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.3;
        }

        .grid-line {
            position: absolute;
            background: rgba(0, 255, 136, 0.2);
        }

        .grid-line.horizontal {
            left: 0;
            right: 0;
            height: 1px;
        }

        .grid-line.vertical {
            top: 0;
            bottom: 0;
            width: 1px;
        }

        .data-cubes {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .data-cube {
            position: absolute;
            width: 40px;
            height: var(--height, 50px);
            background: var(--color, #00ff88);
            border-radius: 4px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .data-cube:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
        }

        .cube-label {
            position: absolute;
            bottom: -30px;
            font-size: 0.7rem;
            text-align: center;
            color: #ffffff;
            font-family: 'Orbitron', monospace;
            white-space: nowrap;
        }

        .scene-info {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 150px;
        }

        .info-panel {
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 4px;
            padding: 10px;
        }

        .info-title {
            font-size: 0.8rem;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 8px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 0.7rem;
        }

        .stat-label {
            color: #ffffff;
            opacity: 0.8;
        }

        .stat-value {
            color: #00ff88;
            font-family: 'Orbitron', monospace;
        }

        .fallback-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .control-button {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            color: #00ff88;
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-button:hover {
            background: rgba(0, 255, 136, 0.2);
            transform: translateY(-1px);
        }

        @keyframes cubeFloat {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        /* 高级流程控制台样式 */
        .advanced-control-panel {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.05), rgba(0, 0, 0, 0.3));
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 12px;
            padding: 0;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .panel-header-advanced {
            background: linear-gradient(90deg, rgba(0, 255, 136, 0.1), rgba(0, 255, 136, 0.05));
            border-bottom: 1px solid rgba(0, 255, 136, 0.2);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-indicator-advanced {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-indicator-advanced.online {
            background: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .panel-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #00ff88;
            font-family: 'Orbitron', monospace;
        }

        .panel-version {
            font-size: 0.7rem;
            color: #ff6600;
            background: rgba(255, 102, 0, 0.1);
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid rgba(255, 102, 0, 0.3);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .flow-metrics {
            display: flex;
            gap: 15px;
        }

        .metric-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }

        .metric-label {
            font-size: 0.7rem;
            color: #8b949e;
            text-transform: uppercase;
        }

        .metric-value {
            font-size: 0.9rem;
            font-weight: bold;
            color: #00ff88;
            font-family: 'Orbitron', monospace;
        }

        .panel-controls-advanced {
            display: flex;
            gap: 8px;
        }

        .control-btn-advanced {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            color: #00ff88;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .control-btn-advanced:hover {
            background: rgba(0, 255, 136, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 255, 136, 0.3);
        }

        .control-btn-advanced.primary {
            background: rgba(0, 255, 136, 0.15);
            border-color: #00ff88;
        }

        .control-btn-advanced.secondary {
            background: rgba(0, 102, 204, 0.1);
            border-color: rgba(0, 102, 204, 0.3);
            color: #0066cc;
        }

        .control-btn-advanced.tertiary {
            background: rgba(255, 102, 0, 0.1);
            border-color: rgba(255, 102, 0, 0.3);
            color: #ff6600;
        }

        .control-btn-advanced.quaternary {
            background: rgba(139, 148, 158, 0.1);
            border-color: rgba(139, 148, 158, 0.3);
            color: #8b949e;
        }

        .flow-workspace {
            display: flex;
            height: 400px;
        }

        .workspace-sidebar {
            width: 250px;
            background: rgba(0, 0, 0, 0.2);
            border-right: 1px solid rgba(0, 255, 136, 0.1);
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .status-item.active {
            background: rgba(0, 255, 136, 0.05);
            border: 1px solid rgba(0, 255, 136, 0.1);
        }

        .status-item.pending {
            background: rgba(139, 148, 158, 0.05);
            border: 1px solid rgba(139, 148, 158, 0.1);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.running {
            background: #00ff88;
            box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
        }

        .status-dot.warning {
            background: #ffff00;
            box-shadow: 0 0 8px rgba(255, 255, 0, 0.5);
        }

        .status-dot.pending {
            background: #8b949e;
            animation: none;
        }

        .status-progress {
            margin-left: auto;
            font-size: 0.8rem;
            font-weight: bold;
            color: #00ff88;
            font-family: 'Orbitron', monospace;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .metric-card {
            background: rgba(0, 255, 136, 0.05);
            border: 1px solid rgba(0, 255, 136, 0.1);
            border-radius: 6px;
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            background: rgba(0, 255, 136, 0.1);
            transform: translateY(-1px);
        }

        .metric-icon {
            width: 32px;
            height: 32px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #00ff88;
        }

        .metric-info {
            flex: 1;
        }

        .metric-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: #00ff88;
            font-family: 'Orbitron', monospace;
            line-height: 1;
        }

        .metric-desc {
            font-size: 0.7rem;
            color: #8b949e;
            text-transform: uppercase;
        }

        .workspace-main {
            flex: 1;
            padding: 20px;
            position: relative;
        }

        .flow-display-advanced {
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(0, 255, 136, 0.02), transparent);
            border: 1px dashed rgba(0, 255, 136, 0.2);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        /* 里程碑页面增强对比度样式 */
        #milestones .dashboard-card {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.05), rgba(0, 0, 0, 0.4)) !important;
            border: 1px solid rgba(0, 255, 136, 0.2) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
        }

        #milestones .card-title {
            color: #ffffff !important;
            font-weight: 600 !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
        }

        #milestones .card-title i {
            color: #00ff88 !important;
        }

        #milestones .timeline-container {
            background: rgba(0, 0, 0, 0.2) !important;
            border-radius: 8px !important;
            padding: 10px !important;
        }

        /* 改善按钮对比度 */
        #milestones .btn-outline-success {
            color: #00ff88 !important;
            border-color: #00ff88 !important;
            background: rgba(0, 255, 136, 0.1) !important;
            font-weight: 600 !important;
        }

        #milestones .btn-outline-success:hover {
            background: rgba(0, 255, 136, 0.2) !important;
            transform: translateY(-1px) !important;
        }

        #milestones .btn-outline-primary {
            color: #0066cc !important;
            border-color: #0066cc !important;
            background: rgba(0, 102, 204, 0.1) !important;
            font-weight: 600 !important;
        }

        #milestones .btn-outline-primary:hover {
            background: rgba(0, 102, 204, 0.2) !important;
            transform: translateY(-1px) !important;
        }

        /* 改善表单选择器对比度 */
        #milestones .form-select {
            background: rgba(0, 0, 0, 0.4) !important;
            border: 1px solid rgba(0, 255, 136, 0.3) !important;
            color: #ffffff !important;
            font-weight: 500 !important;
        }

        #milestones .form-select:focus {
            border-color: #00ff88 !important;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3) !important;
        }

        /* 改善徽章对比度 */
        #milestones .badge {
            font-weight: 600 !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .machine-display {
                width: 95vw;
                height: 95vh;
            }
        }

        @media (max-width: 768px) {
            .system-title {
                font-size: 1rem;
            }

            .nav-tab {
                padding: 12px 20px;
                font-size: 0.9rem;
            }

            .content-panel {
                padding: 15px;
            }
        }

        /* 数据挖掘控制台样式 */
        .mining-metric {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(0, 102, 204, 0.1));
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .mining-metric:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
            border-color: rgba(0, 255, 136, 0.6);
        }

        .mining-metric-icon {
            font-size: 2rem;
            color: #00ff88;
            margin-bottom: 10px;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .mining-metric-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 5px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .mining-metric-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        /* 动画效果 */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* 3D渲染指标样式 */
        .render-metric {
            background: linear-gradient(135deg, rgba(0, 102, 204, 0.1), rgba(0, 255, 136, 0.1));
            border: 1px solid rgba(0, 102, 204, 0.3);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .render-metric::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .render-metric:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 102, 204, 0.4);
            border-color: rgba(0, 255, 136, 0.6);
        }

        .render-metric:hover::before {
            left: 100%;
        }

        .render-metric-icon {
            font-size: 1.5rem;
            color: #0066cc;
            margin-bottom: 8px;
            text-shadow: 0 0 8px rgba(0, 102, 204, 0.5);
        }

        .render-metric-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 3px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .render-metric-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        /* 3D控制项样式 */
        .control-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            height: 100%;
        }

        .control-item:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(0, 255, 136, 0.3);
        }

        /* 数据挖掘控制面板样式 */
        .data-mining-panel {
            background: linear-gradient(135deg, rgba(0, 102, 204, 0.1), rgba(0, 255, 136, 0.05));
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 12px;
            padding: 20px;
            height: 600px;
            overflow-y: auto;
        }

        .control-section {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
        }

        .control-title {
            color: #00ff88;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dimension-item, .filter-item {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            padding: 12px;
        }

        .dimension-item label, .filter-item label {
            color: #ffffff;
            font-weight: 500;
            font-size: 0.85rem;
            margin-bottom: 8px;
        }

        .analysis-results {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 12px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.8rem;
        }

        .result-value {
            color: #ffffff;
            font-weight: 600;
            font-size: 0.85rem;
        }

        /* 滚动条样式 */
        .data-mining-panel::-webkit-scrollbar {
            width: 6px;
        }

        .data-mining-panel::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .data-mining-panel::-webkit-scrollbar-thumb {
            background: rgba(0, 255, 136, 0.5);
            border-radius: 3px;
        }

        .data-mining-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 255, 136, 0.7);
        }
    </style>
</head>
<body>
    <div class="machine-display">
        <!-- 顶部状态栏 -->
        <div class="status-bar">
            <div class="system-title">
                <i class="fas fa-microchip"></i>
                项目里程碑达成率统计与趋势预测系统
            </div>
            <div class="system-status">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>系统运行</span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-clock"></i>
                    <span id="current-time"></span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-database"></i>
                    <span>数据同步</span>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 标签页导航 -->
            <div class="nav-tabs-custom">
                <button class="nav-tab active" data-tab="overview">
                    <i class="fas fa-tachometer-alt"></i>
                    项目概览
                </button>
                <button class="nav-tab" data-tab="milestones">
                    <i class="fas fa-flag-checkered"></i>
                    里程碑追踪
                </button>
                <button class="nav-tab" data-tab="trends">
                    <i class="fas fa-chart-line"></i>
                    趋势预测
                </button>
                <button class="nav-tab" data-tab="analytics">
                    <i class="fas fa-analytics"></i>
                    深度分析
                </button>
                <button class="nav-tab" data-tab="3d-view">
                    <i class="fas fa-cube"></i>
                    3D视图
                </button>
            </div>

            <!-- 内容面板 -->
            <div class="content-panel">
                <!-- 项目概览标签页 -->
                <div id="overview" class="tab-content active">
                    <!-- 系统控制台区域 -->
                    <div class="control-console mb-3">
                        <div class="row g-2">
                            <!-- 主要KPI指标 -->
                            <div class="col-xl-8">
                                <div class="kpi-panel">
                                    <div class="panel-header">
                                        <i class="fas fa-tachometer-alt"></i>
                                        <span>核心性能指标 (KPI)</span>
                                        <div class="status-indicator online"></div>
                                    </div>
                                    <div class="kpi-grid">
                                        <div class="kpi-item primary">
                                            <div class="kpi-icon"><i class="fas fa-percentage"></i></div>
                                            <div class="kpi-content">
                                                <div class="kpi-value"><span id="completion-rate">87.3</span>%</div>
                                                <div class="kpi-label">总体达成率</div>
                                                <div class="kpi-trend up">+3.2%</div>
                                            </div>
                                        </div>
                                        <div class="kpi-item secondary">
                                            <div class="kpi-icon"><i class="fas fa-project-diagram"></i></div>
                                            <div class="kpi-content">
                                                <div class="kpi-value"><span id="active-projects">12</span></div>
                                                <div class="kpi-label">活跃项目</div>
                                                <div class="kpi-trend up">+2</div>
                                            </div>
                                        </div>
                                        <div class="kpi-item success">
                                            <div class="kpi-icon"><i class="fas fa-flag"></i></div>
                                            <div class="kpi-content">
                                                <div class="kpi-value"><span id="completed-milestones">156</span></div>
                                                <div class="kpi-label">已完成里程碑</div>
                                                <div class="kpi-trend up">+8</div>
                                            </div>
                                        </div>
                                        <div class="kpi-item warning">
                                            <div class="kpi-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                            <div class="kpi-content">
                                                <div class="kpi-value"><span id="risk-projects">3</span></div>
                                                <div class="kpi-label">风险项目</div>
                                                <div class="kpi-trend down">-1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 系统状态监控 -->
                            <div class="col-xl-4">
                                <div class="system-monitor">
                                    <div class="panel-header">
                                        <i class="fas fa-desktop"></i>
                                        <span>系统监控</span>
                                        <div class="status-indicator online"></div>
                                    </div>
                                    <div class="monitor-grid">
                                        <div class="monitor-item">
                                            <div class="monitor-label">CPU使用率</div>
                                            <div class="monitor-bar">
                                                <div class="bar-fill" style="width: 45%; background: #00ff88;"></div>
                                            </div>
                                            <div class="monitor-value">45%</div>
                                        </div>
                                        <div class="monitor-item">
                                            <div class="monitor-label">内存使用率</div>
                                            <div class="monitor-bar">
                                                <div class="bar-fill" style="width: 67%; background: #ffff00;"></div>
                                            </div>
                                            <div class="monitor-value">67%</div>
                                        </div>
                                        <div class="monitor-item">
                                            <div class="monitor-label">网络负载</div>
                                            <div class="monitor-bar">
                                                <div class="bar-fill" style="width: 23%; background: #00ff88;"></div>
                                            </div>
                                            <div class="monitor-value">23%</div>
                                        </div>
                                        <div class="monitor-item">
                                            <div class="monitor-label">数据同步</div>
                                            <div class="monitor-bar">
                                                <div class="bar-fill" style="width: 100%; background: #00ff88;"></div>
                                            </div>
                                            <div class="monitor-value">100%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析与监控区域 -->
                    <div class="analysis-section">
                        <div class="row g-3">
                            <!-- 左侧：核心分析图表 -->
                            <div class="col-xl-9">
                                <div class="analysis-panel">
                                    <div class="panel-header">
                                        <i class="fas fa-chart-area"></i>
                                        <span>项目性能分析中心</span>
                                        <div class="panel-controls">
                                            <button class="control-btn" onclick="refreshAnalysis()">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button class="control-btn" onclick="exportAnalysis()">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="control-btn" onclick="toggleFullscreen()">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 分析图表网格 -->
                                    <div class="analysis-chart-grid">
                                        <!-- 多维性能雷达框 -->
                                        <div class="chart-panel radar-panel">
                                            <div class="chart-panel-header">
                                                <span class="chart-title">多维性能雷达</span>
                                                <div class="chart-status active"></div>
                                            </div>
                                            <div id="radar-chart" class="chart-panel-content"></div>
                                        </div>

                                        <!-- 项目活跃度矩阵框 -->
                                        <div class="chart-panel heatmap-panel">
                                            <div class="chart-panel-header">
                                                <span class="chart-title">项目活跃度矩阵</span>
                                                <div class="chart-status active"></div>
                                            </div>
                                            <div id="heatmap-chart" class="chart-panel-content"></div>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <!-- 右侧：实时监控面板 -->
                            <div class="col-xl-3">
                                <div class="monitoring-panel">
                                    <div class="panel-header">
                                        <i class="fas fa-eye"></i>
                                        <span>实时监控</span>
                                        <div class="status-indicator online"></div>
                                    </div>

                                    <!-- 项目状态监控 -->
                                    <div class="monitor-section">
                                        <div class="section-title">项目状态</div>
                                        <div id="project-status-list" class="status-monitor">
                                            <!-- 项目状态列表 -->
                                        </div>
                                    </div>

                                    <!-- 实时指标 -->
                                    <div class="monitor-section">
                                        <div class="section-title">实时指标</div>
                                        <div class="metrics-display">
                                            <div class="metric-row">
                                                <span class="metric-name">今日任务</span>
                                                <span class="metric-value">24</span>
                                            </div>
                                            <div class="metric-row">
                                                <span class="metric-name">已完成</span>
                                                <span class="metric-value success">18</span>
                                            </div>
                                            <div class="metric-row">
                                                <span class="metric-name">进行中</span>
                                                <span class="metric-value warning">6</span>
                                            </div>
                                            <div class="metric-row">
                                                <span class="metric-name">延期</span>
                                                <span class="metric-value danger">2</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 告警信息 -->
                                    <div class="monitor-section">
                                        <div class="section-title">系统告警</div>
                                        <div class="alert-display">
                                            <div class="alert-item warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                <span>项目C进度滞后</span>
                                            </div>
                                            <div class="alert-item info">
                                                <i class="fas fa-info-circle"></i>
                                                <span>数据同步完成</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 高级流程控制区域 -->
                    <div class="process-control mt-3">
                        <div class="advanced-control-panel">
                            <div class="panel-header-advanced">
                                <div class="header-left">
                                    <div class="status-indicator-advanced online"></div>
                                    <i class="fas fa-project-diagram"></i>
                                    <span class="panel-title">智能项目流程控制台</span>
                                    <span class="panel-version">v2.1</span>
                                </div>
                                <div class="header-right">
                                    <div class="flow-metrics">
                                        <div class="metric-item">
                                            <span class="metric-label">活跃节点</span>
                                            <span class="metric-value">4/7</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="metric-label">完成率</span>
                                            <span class="metric-value">68%</span>
                                        </div>
                                    </div>
                                    <div class="panel-controls-advanced">
                                        <button class="control-btn-advanced primary" onclick="animateFlowChart()">
                                            <i class="fas fa-play"></i> 智能演示
                                        </button>
                                        <button class="control-btn-advanced secondary" onclick="refreshFlowChart()">
                                            <i class="fas fa-sync-alt"></i> 实时刷新
                                        </button>
                                        <button class="control-btn-advanced tertiary" onclick="configureFlow()">
                                            <i class="fas fa-cogs"></i> 高级配置
                                        </button>
                                        <button class="control-btn-advanced quaternary" onclick="exportFlow()">
                                            <i class="fas fa-download"></i> 导出
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="flow-workspace">
                                <div class="workspace-sidebar">
                                    <div class="sidebar-section">
                                        <div class="section-title">流程状态</div>
                                        <div class="status-list">
                                            <div class="status-item active">
                                                <div class="status-dot running"></div>
                                                <span>需求分析</span>
                                                <span class="status-progress">100%</span>
                                            </div>
                                            <div class="status-item active">
                                                <div class="status-dot running"></div>
                                                <span>设计开发</span>
                                                <span class="status-progress">85%</span>
                                            </div>
                                            <div class="status-item active">
                                                <div class="status-dot warning"></div>
                                                <span>测试验证</span>
                                                <span class="status-progress">45%</span>
                                            </div>
                                            <div class="status-item pending">
                                                <div class="status-dot pending"></div>
                                                <span>部署实施</span>
                                                <span class="status-progress">0%</span>
                                            </div>
                                            <div class="status-item pending">
                                                <div class="status-dot pending"></div>
                                                <span>运维监控</span>
                                                <span class="status-progress">0%</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="sidebar-section">
                                        <div class="section-title">关键指标</div>
                                        <div class="metrics-grid">
                                            <div class="metric-card">
                                                <div class="metric-icon">
                                                    <i class="fas fa-clock"></i>
                                                </div>
                                                <div class="metric-info">
                                                    <div class="metric-number">24</div>
                                                    <div class="metric-desc">天剩余</div>
                                                </div>
                                            </div>
                                            <div class="metric-card">
                                                <div class="metric-icon">
                                                    <i class="fas fa-users"></i>
                                                </div>
                                                <div class="metric-info">
                                                    <div class="metric-number">12</div>
                                                    <div class="metric-desc">团队成员</div>
                                                </div>
                                            </div>
                                            <div class="metric-card">
                                                <div class="metric-icon">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                </div>
                                                <div class="metric-info">
                                                    <div class="metric-number">3</div>
                                                    <div class="metric-desc">风险项</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="workspace-main">
                                    <div id="flow-chart" class="flow-display-advanced"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 里程碑追踪标签页 -->
                <div id="milestones" class="tab-content">
                    <!-- 里程碑时间线 -->
                    <div class="row g-3 mb-3">
                        <div class="col-12">
                            <div class="dashboard-card chart-card">
                                <div class="card-title">
                                    <i class="fas fa-flag-checkered"></i>
                                    里程碑时间线
                                    <div class="card-actions">
                                        <button class="btn btn-sm btn-outline-success" onclick="addMilestone()">
                                            <i class="fas fa-plus"></i> 新增
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary" onclick="refreshMilestones()">
                                            <i class="fas fa-sync-alt"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                                <div id="milestone-timeline" class="timeline-container">
                                    <!-- 时间线将在这里生成 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <!-- 左侧：里程碑详细列表和风险评估 -->
                        <div class="col-xl-8 col-lg-7">
                            <div class="row g-3">
                                <!-- 里程碑详细列表 -->
                                <div class="col-12">
                                    <div class="dashboard-card info-card">
                                        <div class="card-title">
                                            <i class="fas fa-list"></i>
                                            里程碑详细信息
                                            <div class="card-actions">
                                                <select class="form-select form-select-sm" style="width: 120px;" onchange="filterMilestones(this.value)">
                                                    <option value="all">全部状态</option>
                                                    <option value="completed">已完成</option>
                                                    <option value="active">进行中</option>
                                                    <option value="pending">待开始</option>
                                                    <option value="delayed">延期</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div id="milestone-list" class="milestone-list">
                                            <!-- 里程碑列表将在这里生成 -->
                                        </div>
                                    </div>
                                </div>

                                <!-- 里程碑风险评估 -->
                                <div class="col-12">
                                    <div class="dashboard-card chart-card">
                                        <div class="card-title">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            风险评估与预警
                                            <div class="card-actions">
                                                <button class="btn btn-sm btn-outline-warning" onclick="refreshRiskAssessment()">
                                                    <i class="fas fa-sync-alt"></i> 刷新
                                                </button>
                                            </div>
                                        </div>
                                        <div id="risk-assessment" class="risk-assessment-container">
                                            <!-- 风险评估内容将在这里生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：统计和即将到期 -->
                        <div class="col-xl-4 col-lg-5">
                            <div class="row g-3">
                                <!-- 里程碑统计 -->
                                <div class="col-12">
                                    <div class="dashboard-card chart-card">
                                        <div class="card-title">
                                            <i class="fas fa-chart-pie"></i>
                                            里程碑统计分布
                                        </div>
                                        <div id="milestone-stats-chart" class="chart-container" style="height: 250px;"></div>
                                    </div>
                                </div>

                                <!-- 即将到期 -->
                                <div class="col-12">
                                    <div class="dashboard-card info-card">
                                        <div class="card-title">
                                            <i class="fas fa-calendar-alt"></i>
                                            即将到期提醒
                                            <span class="badge bg-warning ms-2">4</span>
                                        </div>
                                        <div id="upcoming-milestones" class="upcoming-list">
                                            <!-- 即将到期的里程碑 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 趋势预测标签页 -->
                <div id="trends" class="tab-content">
                    <!-- AI控制面板 -->
                    <div class="row g-3 mb-3">
                        <div class="col-12">
                            <div class="dashboard-card ai-control-panel">
                                <div class="card-title">
                                    <i class="fas fa-brain"></i>
                                    AI趋势预测控制台
                                    <div class="card-actions">
                                        <select class="form-select form-select-sm me-2" style="width: 140px;" onchange="changePredictionModel(this.value)">
                                            <option value="neural">神经网络</option>
                                            <option value="ensemble">集成学习</option>
                                            <option value="polynomial">多项式回归</option>
                                            <option value="linear">线性回归</option>
                                        </select>
                                        <button class="btn btn-sm btn-outline-warning me-2" onclick="runPrediction()">
                                            <i class="fas fa-play"></i> 运行预测
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="exportPrediction()">
                                            <i class="fas fa-download"></i> 导出
                                        </button>
                                    </div>
                                </div>
                                <div class="ai-metrics-grid">
                                    <div class="ai-metric">
                                        <div class="ai-metric-icon">
                                            <i class="fas fa-bullseye"></i>
                                        </div>
                                        <div class="ai-metric-content">
                                            <div class="ai-metric-value">94.2%</div>
                                            <div class="ai-metric-label">预测准确率</div>
                                        </div>
                                    </div>
                                    <div class="ai-metric">
                                        <div class="ai-metric-icon">
                                            <i class="fas fa-chart-area"></i>
                                        </div>
                                        <div class="ai-metric-content">
                                            <div class="ai-metric-value">±3.5%</div>
                                            <div class="ai-metric-label">置信区间</div>
                                        </div>
                                    </div>
                                    <div class="ai-metric">
                                        <div class="ai-metric-icon">
                                            <i class="fas fa-calendar-check"></i>
                                        </div>
                                        <div class="ai-metric-content">
                                            <div class="ai-metric-value">12周</div>
                                            <div class="ai-metric-label">预测周期</div>
                                        </div>
                                    </div>
                                    <div class="ai-metric">
                                        <div class="ai-metric-icon">
                                            <i class="fas fa-sync-alt"></i>
                                        </div>
                                        <div class="ai-metric-content">
                                            <div class="ai-metric-value">实时</div>
                                            <div class="ai-metric-label">数据更新</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 主要预测图表 -->
                    <div class="row g-3 mb-3">
                        <div class="col-12">
                            <div class="dashboard-card chart-card">
                                <div class="card-title">
                                    <i class="fas fa-chart-line"></i>
                                    项目达成率趋势预测分析
                                    <div class="card-actions">
                                        <button class="btn btn-sm btn-outline-info" onclick="togglePredictionView()">
                                            <i class="fas fa-eye"></i> 切换视图
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary" onclick="refreshTrendChart()">
                                            <i class="fas fa-sync-alt"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                                <div id="trend-prediction-chart" class="chart-container" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <!-- 左侧分析区 -->
                        <div class="col-xl-8 col-lg-7">
                            <div class="row g-3">
                                <!-- 关联分析 -->
                                <div class="col-md-6">
                                    <div class="dashboard-card chart-card">
                                        <div class="card-title">
                                            <i class="fas fa-project-diagram"></i>
                                            多维度关联分析
                                        </div>
                                        <div id="correlation-matrix" class="chart-container" style="height: 280px;"></div>
                                    </div>
                                </div>

                                <!-- 季节性分析 -->
                                <div class="col-md-6">
                                    <div class="dashboard-card chart-card">
                                        <div class="card-title">
                                            <i class="fas fa-calendar-week"></i>
                                            季节性模式分析
                                        </div>
                                        <div id="seasonal-analysis" class="chart-container" style="height: 280px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧预警区 -->
                        <div class="col-xl-4 col-lg-5">
                            <div class="row g-3">
                                <!-- 预警信息 -->
                                <div class="col-12">
                                    <div class="dashboard-card info-card">
                                        <div class="card-title">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            智能预警
                                            <span class="badge bg-danger ms-2">3</span>
                                        </div>
                                        <div id="warning-alerts" class="warning-list">
                                            <!-- 预警信息列表 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 深度分析标签页 -->
                <div id="analytics" class="tab-content">
                    <div class="row g-3">
                        <!-- 数据挖掘控制台 -->
                        <div class="col-12">
                            <div class="dashboard-card">
                                <div class="card-title">
                                    <i class="fas fa-microscope"></i>
                                    深度数据挖掘控制台
                                    <div class="ms-auto">
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="runDataMining()">
                                            <i class="fas fa-search"></i> 开始挖掘
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="generateInsights()">
                                            <i class="fas fa-lightbulb"></i> 生成洞察
                                        </button>
                                    </div>
                                </div>
                                <!-- 数据挖掘指标面板 -->
                                <div class="row g-3 mb-3">
                                    <div class="col-md-3">
                                        <div class="mining-metric">
                                            <div class="mining-metric-icon">
                                                <i class="fas fa-database"></i>
                                            </div>
                                            <div class="mining-metric-content">
                                                <div class="mining-metric-value">2.4TB</div>
                                                <div class="mining-metric-label">数据总量</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mining-metric">
                                            <div class="mining-metric-icon">
                                                <i class="fas fa-search"></i>
                                            </div>
                                            <div class="mining-metric-content">
                                                <div class="mining-metric-value">847</div>
                                                <div class="mining-metric-label">模式识别</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mining-metric">
                                            <div class="mining-metric-icon">
                                                <i class="fas fa-brain"></i>
                                            </div>
                                            <div class="mining-metric-content">
                                                <div class="mining-metric-value">96.8%</div>
                                                <div class="mining-metric-label">算法精度</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mining-metric">
                                            <div class="mining-metric-icon">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                            <div class="mining-metric-content">
                                                <div class="mining-metric-value">3.2s</div>
                                                <div class="mining-metric-label">处理速度</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 挖掘算法选择 -->
                                <div class="row g-3 mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label" style="color: #ffffff; font-weight: 600;">挖掘算法</label>
                                        <select class="form-select" onchange="changeMiningAlgorithm(this.value)">
                                            <option value="clustering">聚类分析</option>
                                            <option value="association">关联规则</option>
                                            <option value="classification">分类预测</option>
                                            <option value="regression">回归分析</option>
                                            <option value="anomaly">异常检测</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label" style="color: #ffffff; font-weight: 600;">数据范围</label>
                                        <select class="form-select" onchange="changeDataRange(this.value)">
                                            <option value="last7days">最近7天</option>
                                            <option value="last30days">最近30天</option>
                                            <option value="last90days">最近90天</option>
                                            <option value="lastyear">最近一年</option>
                                            <option value="all">全部数据</option>
                                        </select>
                                    </div>
                                </div>

                                <div id="mining-progress" style="display: none;">
                                    <div class="progress mb-2">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
                                    </div>
                                    <div class="text-center">
                                        <small style="color: #ffffff; font-weight: 500; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);">正在分析数据模式...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3 mt-3">
                        <!-- 网络关系图 -->
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="card-title">
                                    <i class="fas fa-project-diagram"></i>
                                    项目依赖关系网络
                                </div>
                                <div id="network-graph" style="height: 350px;"></div>
                            </div>
                        </div>

                        <!-- 性能指标雷达 -->
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="card-title">
                                    <i class="fas fa-tachometer-alt"></i>
                                    多维性能分析
                                </div>
                                <div id="performance-radar" style="height: 350px;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3 mt-3">
                        <!-- 异常检测 -->
                        <div class="col-md-8">
                            <div class="dashboard-card">
                                <div class="card-title">
                                    <i class="fas fa-exclamation-circle"></i>
                                    异常模式检测
                                </div>
                                <div id="anomaly-detection" style="height: 300px;"></div>
                            </div>
                        </div>

                        <!-- 智能建议 -->
                        <div class="col-md-4">
                            <div class="dashboard-card">
                                <div class="card-title">
                                    <i class="fas fa-robot"></i>
                                    AI智能建议
                                </div>
                                <div id="ai-suggestions" style="height: 300px; overflow-y: auto;">
                                    <!-- AI建议列表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 3D视图标签页 -->
                <div id="3d-view" class="tab-content">
                    <div class="row g-3">
                        <!-- 3D控制面板 -->
                        <div class="col-12">
                            <div class="dashboard-card">
                                <div class="card-title">
                                    <i class="fas fa-cube"></i>
                                    3D项目可视化控制台
                                    <div class="ms-auto">
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="reset3DView()">
                                            <i class="fas fa-undo"></i> 重置视角
                                        </button>
                                        <button class="btn btn-sm btn-outline-success me-2" onclick="animate3DScene()">
                                            <i class="fas fa-play"></i> 动画演示
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" onclick="toggle3DMode()">
                                            <i class="fas fa-eye"></i> 切换模式
                                        </button>
                                    </div>
                                </div>
                                <!-- 3D渲染状态指标 -->
                                <div class="row g-3 mb-4">
                                    <div class="col-md-2">
                                        <div class="render-metric">
                                            <div class="render-metric-icon">
                                                <i class="fas fa-tachometer-alt"></i>
                                            </div>
                                            <div class="render-metric-content">
                                                <div class="render-metric-value" id="fps-display">60</div>
                                                <div class="render-metric-label">FPS</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="render-metric">
                                            <div class="render-metric-icon">
                                                <i class="fas fa-cubes"></i>
                                            </div>
                                            <div class="render-metric-content">
                                                <div class="render-metric-value" id="triangles-display">2.4K</div>
                                                <div class="render-metric-label">三角面</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="render-metric">
                                            <div class="render-metric-icon">
                                                <i class="fas fa-memory"></i>
                                            </div>
                                            <div class="render-metric-content">
                                                <div class="render-metric-value" id="memory-display">128MB</div>
                                                <div class="render-metric-label">显存</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="render-metric">
                                            <div class="render-metric-icon">
                                                <i class="fas fa-layer-group"></i>
                                            </div>
                                            <div class="render-metric-content">
                                                <div class="render-metric-value" id="objects-display">15</div>
                                                <div class="render-metric-label">对象</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="render-metric">
                                            <div class="render-metric-icon">
                                                <i class="fas fa-lightbulb"></i>
                                            </div>
                                            <div class="render-metric-content">
                                                <div class="render-metric-value" id="lights-display">8</div>
                                                <div class="render-metric-label">光源</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="render-metric">
                                            <div class="render-metric-icon">
                                                <i class="fas fa-video"></i>
                                            </div>
                                            <div class="render-metric-content">
                                                <div class="render-metric-value" id="render-time">16ms</div>
                                                <div class="render-metric-label">渲染时间</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="control-item">
                                            <label class="form-label" style="color: #ffffff; font-weight: 600;">视角控制</label>
                                            <div class="btn-group-vertical w-100">
                                                <button class="btn btn-sm btn-outline-light" onclick="setView('top')">
                                                    <i class="fas fa-arrow-down"></i> 俯视图
                                                </button>
                                                <button class="btn btn-sm btn-outline-light" onclick="setView('side')">
                                                    <i class="fas fa-arrows-alt-h"></i> 侧视图
                                                </button>
                                                <button class="btn btn-sm btn-outline-light" onclick="setView('perspective')">
                                                    <i class="fas fa-cube"></i> 透视图
                                                </button>
                                                <button class="btn btn-sm btn-outline-light" onclick="setView('isometric')">
                                                    <i class="fas fa-th"></i> 等轴图
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="control-item">
                                            <label class="form-label" style="color: #ffffff; font-weight: 600;">显示模式</label>
                                            <select class="form-select form-select-sm" onchange="change3DMode(this.value)">
                                                <option value="projects">项目结构</option>
                                                <option value="timeline">时间轴</option>
                                                <option value="network">关系网络</option>
                                                <option value="metrics">指标立方体</option>
                                                <option value="heatmap">热力图</option>
                                                <option value="flow">数据流</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="control-item">
                                            <label class="form-label" style="color: #ffffff; font-weight: 600;">渲染质量</label>
                                            <input type="range" class="form-range" min="1" max="4" value="2" onchange="setRenderQuality(this.value)">
                                            <small style="color: rgba(255, 255, 255, 0.7);">低 - 中 - 高 - 极高</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="control-item">
                                            <label class="form-label" style="color: #ffffff; font-weight: 600;">动画速度</label>
                                            <input type="range" class="form-range" min="0.1" max="3" step="0.1" value="1" onchange="setAnimationSpeed(this.value)">
                                            <small style="color: rgba(255, 255, 255, 0.7);">0.1x - 3x</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="control-item">
                                            <label class="form-label" style="color: #ffffff; font-weight: 600;">光照强度</label>
                                            <input type="range" class="form-range" min="0.1" max="2" step="0.1" value="1" onchange="setLightIntensity(this.value)">
                                            <small style="color: rgba(255, 255, 255, 0.7);">暗 - 亮</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="control-item">
                                            <label class="form-label" style="color: #ffffff; font-weight: 600;">特效开关</label>
                                            <div class="form-check form-switch mb-1">
                                                <input class="form-check-input" type="checkbox" id="autoRotate" onchange="toggleAutoRotate(this.checked)">
                                                <label class="form-check-label" for="autoRotate" style="color: #ffffff; font-size: 0.8rem;">自动旋转</label>
                                            </div>
                                            <div class="form-check form-switch mb-1">
                                                <input class="form-check-input" type="checkbox" id="shadows" checked onchange="toggleShadows(this.checked)">
                                                <label class="form-check-label" for="shadows" style="color: #ffffff; font-size: 0.8rem;">阴影效果</label>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="particles" onchange="toggleParticles(this.checked)">
                                                <label class="form-check-label" for="particles" style="color: #ffffff; font-size: 0.8rem;">粒子效果</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3 mt-3">
                        <!-- 主3D场景 -->
                        <div class="col-md-9">
                            <div class="dashboard-card">
                                <div class="card-title">
                                    <i class="fas fa-cube"></i>
                                    项目里程碑3D可视化
                                </div>
                                <div id="three-scene" style="height: 500px; background: radial-gradient(circle, #1a1a1a 0%, #0d1117 100%);"></div>
                            </div>
                        </div>

                        <!-- 3D信息面板 -->
                        <div class="col-md-3">
                            <div class="dashboard-card">
                                <div class="card-title">
                                    <i class="fas fa-info-circle"></i>
                                    场景信息
                                </div>
                                <div id="scene-info">
                                    <div class="info-item mb-3">
                                        <div class="info-label">渲染帧率</div>
                                        <div class="info-value" id="fps-counter">60 FPS</div>
                                    </div>
                                    <div class="info-item mb-3">
                                        <div class="info-label">对象数量</div>
                                        <div class="info-value" id="object-count">12 个</div>
                                    </div>
                                    <div class="info-item mb-3">
                                        <div class="info-label">相机位置</div>
                                        <div class="info-value" id="camera-position">X:0 Y:0 Z:10</div>
                                    </div>
                                </div>
                            </div>

                            <div class="dashboard-card mt-3">
                                <div class="card-title">
                                    <i class="fas fa-mouse-pointer"></i>
                                    交互说明
                                </div>
                                <div class="interaction-help">
                                    <div class="help-item mb-2">
                                        <i class="fas fa-mouse"></i>
                                        <span>左键拖拽: 旋转视角</span>
                                    </div>
                                    <div class="help-item mb-2">
                                        <i class="fas fa-mouse"></i>
                                        <span>右键拖拽: 平移视角</span>
                                    </div>
                                    <div class="help-item mb-2">
                                        <i class="fas fa-mouse"></i>
                                        <span>滚轮: 缩放视角</span>
                                    </div>
                                    <div class="help-item mb-2">
                                        <i class="fas fa-hand-pointer"></i>
                                        <span>点击对象: 查看详情</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- 3D深度数据挖掘控制台 -->
                    <div class="row g-3 mt-3">
                        <div class="col-12">
                            <div class="dashboard-card">
                                <div class="card-title">
                                    <i class="fas fa-cube"></i>
                                    3D深度数据挖掘控制台
                                    <div class="ms-auto">
                                        <select class="form-select form-select-sm me-2" style="width: auto; display: inline-block;" onchange="change3DCubeData(this.value)">
                                            <option value="performance">性能数据立方体</option>
                                            <option value="timeline">时间维度立方体</option>
                                            <option value="resources">资源分配立方体</option>
                                            <option value="quality">质量指标立方体</option>
                                            <option value="risk">风险评估立方体</option>
                                            <option value="prediction">预测分析立方体</option>
                                        </select>
                                        <button class="btn btn-sm btn-outline-warning me-2" onclick="explode3DCube()">
                                            <i class="fas fa-expand"></i> 展开视图
                                        </button>
                                        <button class="btn btn-sm btn-outline-info me-2" onclick="rotate3DCube()">
                                            <i class="fas fa-sync-alt"></i> 旋转
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="analyze3DData()">
                                            <i class="fas fa-search"></i> 深度分析
                                        </button>
                                    </div>
                                </div>

                                <!-- 数据立方体主视图 -->
                                <div class="row g-3">
                                    <div class="col-md-8">
                                        <div id="datacube-3d" style="height: 600px; background: radial-gradient(circle, #1a1a1a 0%, #0d1117 100%); border: 1px solid rgba(0, 255, 136, 0.2); border-radius: 8px;"></div>
                                    </div>

                                    <!-- 数据挖掘控制面板 -->
                                    <div class="col-md-4">
                                        <div class="data-mining-panel">
                                            <!-- 维度控制 -->
                                            <div class="control-section mb-4">
                                                <h6 class="control-title">
                                                    <i class="fas fa-sliders-h"></i> 维度控制
                                                </h6>
                                                <div class="dimension-controls">
                                                    <div class="dimension-item mb-3">
                                                        <label class="form-label">X轴维度</label>
                                                        <select class="form-select form-select-sm" onchange="setXDimension(this.value)">
                                                            <option value="time">时间</option>
                                                            <option value="project">项目</option>
                                                            <option value="team">团队</option>
                                                            <option value="resource">资源</option>
                                                        </select>
                                                    </div>
                                                    <div class="dimension-item mb-3">
                                                        <label class="form-label">Y轴维度</label>
                                                        <select class="form-select form-select-sm" onchange="setYDimension(this.value)">
                                                            <option value="performance">性能</option>
                                                            <option value="quality">质量</option>
                                                            <option value="cost">成本</option>
                                                            <option value="risk">风险</option>
                                                        </select>
                                                    </div>
                                                    <div class="dimension-item mb-3">
                                                        <label class="form-label">Z轴维度</label>
                                                        <select class="form-select form-select-sm" onchange="setZDimension(this.value)">
                                                            <option value="progress">进度</option>
                                                            <option value="satisfaction">满意度</option>
                                                            <option value="efficiency">效率</option>
                                                            <option value="innovation">创新度</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 数据过滤器 -->
                                            <div class="control-section mb-4">
                                                <h6 class="control-title">
                                                    <i class="fas fa-filter"></i> 数据过滤器
                                                </h6>
                                                <div class="filter-controls">
                                                    <div class="filter-item mb-3">
                                                        <label class="form-label">时间范围</label>
                                                        <input type="range" class="form-range" min="1" max="12" value="6" onchange="setTimeRange(this.value)">
                                                        <small class="text-muted">最近 <span id="time-range-value">6</span> 个月</small>
                                                    </div>
                                                    <div class="filter-item mb-3">
                                                        <label class="form-label">数据密度</label>
                                                        <input type="range" class="form-range" min="10" max="100" value="50" onchange="setDataDensity(this.value)">
                                                        <small class="text-muted">密度: <span id="density-value">50</span>%</small>
                                                    </div>
                                                    <div class="filter-item mb-3">
                                                        <label class="form-label">阈值设置</label>
                                                        <input type="range" class="form-range" min="0" max="100" value="75" onchange="setThreshold(this.value)">
                                                        <small class="text-muted">阈值: <span id="threshold-value">75</span>%</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 分析结果 -->
                                            <div class="control-section">
                                                <h6 class="control-title">
                                                    <i class="fas fa-chart-line"></i> 分析结果
                                                </h6>
                                                <div class="analysis-results">
                                                    <div class="result-item mb-2">
                                                        <div class="result-label">数据点数量</div>
                                                        <div class="result-value" id="data-points">2,847</div>
                                                    </div>
                                                    <div class="result-item mb-2">
                                                        <div class="result-label">异常检测</div>
                                                        <div class="result-value text-warning" id="anomalies">12个异常</div>
                                                    </div>
                                                    <div class="result-item mb-2">
                                                        <div class="result-label">相关性系数</div>
                                                        <div class="result-value text-success" id="correlation">0.847</div>
                                                    </div>
                                                    <div class="result-item mb-2">
                                                        <div class="result-label">预测准确度</div>
                                                        <div class="result-value text-info" id="accuracy">94.2%</div>
                                                    </div>
                                                    <div class="result-item mb-2">
                                                        <div class="result-label">趋势方向</div>
                                                        <div class="result-value text-success" id="trend">
                                                            <i class="fas fa-arrow-up"></i> 上升
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量声明
        let scene, camera, renderer, controls;
        let animationId;
        let autoRotateEnabled = false;

        // 直接创建Three.js替代方案，避免CDN和版本问题
        (function() {
            // 避免重复创建
            if (window.threeJSInitialized) {
                return;
            }
            window.threeJSInitialized = true;

            console.log('直接创建Three.js替代方案，避免CDN依赖');

            // 立即创建简化的Three.js替代
            setTimeout(() => {
                if (typeof THREE === 'undefined') {
                    createSimpleThreeJSFallback();
                }
            }, 100);
        })();

        // 基础JavaScript功能
        document.addEventListener('DOMContentLoaded', function() {
            // 更新时间显示
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('zh-CN', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                document.getElementById('current-time').textContent = timeString;
            }

            updateTime();
            setInterval(updateTime, 1000);

            // 定期更新3D渲染指标
            setInterval(() => {
                if (document.getElementById('3d-view').classList.contains('active')) {
                    updateRenderMetrics();
                }
            }, 2000);

            // 标签页切换功能
            const navTabs = document.querySelectorAll('.nav-tab');
            const tabContents = document.querySelectorAll('.tab-content');

            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // 移除所有活动状态
                    navTabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // 添加活动状态
                    this.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');

                    // 根据标签页初始化相应的图表
                    setTimeout(() => {
                        if (targetTab === 'overview') {
                            initOverviewCharts();
                        } else if (targetTab === 'milestones') {
                            initMilestonesCharts();
                        } else if (targetTab === 'trends') {
                            initTrendsCharts();
                        } else if (targetTab === 'analytics') {
                            initAnalyticsCharts();
                        } else if (targetTab === '3d-view') {
                            try {
                                init3DScene();
                                init3DDataCube();
                                updateRenderMetrics();
                                initDataMiningControls();
                            } catch (error) {
                                console.log('3D初始化失败，使用替代方案:', error);
                                // 确保所有3D组件都有替代方案
                                const containers = ['three-scene', 'datacube-3d'];
                                containers.forEach(id => {
                                    const container = document.getElementById(id);
                                    if (container && !container.innerHTML) {
                                        create3DFallback(container);
                                    }
                                });
                            }
                        }
                    }, 100);
                });
            });

            // 初始化概览页面图表
            setTimeout(() => {
                const status = checkLibrariesLoaded();
                initOverviewCharts();
                startDataSimulation();



                // 确保内置图表系统被激活
                setTimeout(() => {
                    if (typeof Chart === 'undefined' && !window.chartFallbacksCreated) {
                        console.log('启动内置高性能图表引擎...');
                        createSimpleChartFallbacks();
                    }
                    // 初始化项目状态列表
                    updateProjectStatusList();
                }, 2000);

                // Three.js使用HTML5 3D引擎
                if (!status.three && !window.threeFallbackCreated) {
                    setTimeout(() => {
                        console.log('启动HTML5 3D渲染引擎...');
                        loadThreeJSFallback();
                    }, 2000);
                }

                // 启动内置图表引擎
                if (!status.chart && !window.chartFallbacksCreated) {
                    setTimeout(createSimpleChartFallbacks, 1000);
                }

                // 显示系统就绪状态
                setTimeout(() => {
                    console.log('✅ 项目里程碑达成率统计与趋势预测系统已就绪');
                    showSystemReadyNotification();
                }, 1000);
            }, 500);
        });

        // 新布局的交互函数
        function refreshAnalysis() {
            console.log('刷新分析数据...');
            // 模拟数据刷新
            const indicators = document.querySelectorAll('.status-indicator');
            indicators.forEach(indicator => {
                indicator.style.animation = 'none';
                setTimeout(() => {
                    indicator.style.animation = 'pulse 2s infinite';
                }, 100);
            });
        }

        function exportAnalysis() {
            console.log('导出分析报告...');
            // 模拟导出功能
            showNotification('分析报告导出中...', 'info');
        }

        function toggleFullscreen() {
            console.log('切换全屏模式...');
            // 模拟全屏切换
            const analysisPanel = document.querySelector('.analysis-panel');
            if (analysisPanel) {
                analysisPanel.style.position = analysisPanel.style.position === 'fixed' ? 'relative' : 'fixed';
                if (analysisPanel.style.position === 'fixed') {
                    analysisPanel.style.top = '0';
                    analysisPanel.style.left = '0';
                    analysisPanel.style.width = '100vw';
                    analysisPanel.style.height = '100vh';
                    analysisPanel.style.zIndex = '9999';
                    analysisPanel.style.background = '#1a1a1a';
                }
            }
        }

        function refreshProjectStatus() {
            console.log('刷新项目状态...');
            // 模拟状态刷新
            updateProjectStatusList();
        }

        function configureFlow() {
            console.log('配置流程参数...');
            // 模拟配置界面
            showNotification('流程配置界面开发中...', 'info');
        }

        function updateProjectStatusList() {
            const statusList = document.getElementById('project-status-list');
            if (statusList) {
                statusList.innerHTML = `
                    <div class="status-item">
                        <div class="status-dot success"></div>
                        <div class="status-info">
                            <div class="status-name">项目Alpha</div>
                            <div class="status-progress">进度: 92%</div>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-dot warning"></div>
                        <div class="status-info">
                            <div class="status-name">项目Beta</div>
                            <div class="status-progress">进度: 78%</div>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-dot success"></div>
                        <div class="status-info">
                            <div class="status-name">项目Gamma</div>
                            <div class="status-progress">进度: 95%</div>
                        </div>
                    </div>
                `;
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'info' ? 'info-circle' : type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                <span>${message}</span>
            `;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 255, 136, 0.1);
                border: 1px solid #00ff88;
                color: #00ff88;
                padding: 12px 20px;
                border-radius: 6px;
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 页面完全加载后的最终检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('页面完全加载，执行最终检查...');
                // 确保内置图表引擎被激活
                if (typeof Chart === 'undefined' && !window.chartFallbacksCreated) {
                    console.log('启动内置高性能图表引擎');
                    createSimpleChartFallbacks();
                }



                const heatmapChart = document.getElementById('heatmap-chart');
                if (heatmapChart && (heatmapChart.innerHTML.trim() === '' || heatmapChart.innerHTML.includes('图表库加载中')) && !window.chartFallbacksCreated) {
                    console.log('热力图为空，激活替代方案');
                    createSimpleChartFallbacks();
                }
            }, 3000);
        });

        // 检查必要的库是否加载
        function checkLibrariesLoaded() {
            const status = {
                plotly: typeof Plotly !== 'undefined',
                d3: typeof d3 !== 'undefined',
                three: typeof THREE !== 'undefined',
                chart: typeof Chart !== 'undefined'
            };

            console.log('库加载状态:', status);

            // 如果Three.js未加载，使用HTML5 3D引擎
            if (!status.three) {
                console.log('启动HTML5 3D渲染引擎...');
                loadThreeJSFallback();
            }

            // 使用Chart.js的HTML替代方案
            if (!status.chart && !window.chartFallbacksCreated) {
                if (!window.chartWarningShown) {
                    console.log('Chart.js 未加载，使用内置图表替代方案...');
                    window.chartWarningShown = true;
                }
                setTimeout(createSimpleChartFallbacks, 500);
            }

            if (!status.plotly) {
                console.warn('Plotly.js 未加载');
            }
            if (!status.d3) {
                console.warn('D3.js 未加载');
            }

            return status;
        }

        // 显示系统就绪通知
        function showSystemReadyNotification() {
            // 检查库加载状态
            const status = checkLibrariesLoaded();
            const loadedLibs = [];

            if (status.plotly) loadedLibs.push('Plotly.js');
            if (status.d3) loadedLibs.push('D3.js');
            if (status.three) loadedLibs.push('Three.js');
            else loadedLibs.push('HTML5 3D引擎');

            if (status.chart) loadedLibs.push('Chart.js');
            else loadedLibs.push('内置图表引擎');

            // 创建一个临时的通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, rgba(0, 255, 136, 0.15), rgba(0, 255, 136, 0.05));
                border: 1px solid #00ff88;
                color: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
                z-index: 10000;
                font-family: 'Orbitron', monospace;
                font-size: 0.85rem;
                animation: slideIn 0.5s ease-out;
                max-width: 380px;
                backdrop-filter: blur(10px);
            `;

            notification.innerHTML = `
                <div style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 10px; color: #00ff88; font-weight: bold; margin-bottom: 10px;">
                        <i class="fas fa-check-circle"></i>
                        <span>项目里程碑达成率统计与趋势预测系统</span>
                    </div>
                    <div style="font-size: 0.8rem; color: #ffffff; opacity: 0.9;">
                        系统已成功启动并就绪
                    </div>
                </div>

                <div style="border-top: 1px solid rgba(0, 255, 136, 0.2); padding-top: 12px;">
                    <div style="margin-bottom: 8px;">
                        <span style="color: #00ff88;">🚀 渲染引擎:</span>
                        <span style="color: #ffffff; opacity: 0.9;">${loadedLibs.length > 0 ? loadedLibs.join(', ') : '基础系统'}</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span style="color: #00ff88;">📊 图表系统:</span>
                        <span style="color: #ffffff; opacity: 0.9;">高性能工业级可视化</span>
                    </div>
                    <div>
                        <span style="color: #00ff88;">🎯 状态:</span>
                        <span style="color: #ffffff; opacity: 0.9;">所有功能正常运行</span>
                    </div>
                </div>
            `;

            // 添加CSS动画
            if (!document.getElementById('notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = `
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOut {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // 5秒后自动消失
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.5s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 500);
            }, 5000);
        }

        // Three.js备用加载 - 直接使用HTML替代方案
        function loadThreeJSFallback() {
            // 检查是否已经有Three.js
            if (typeof THREE !== 'undefined') {
                console.log('Three.js已加载，跳过备用加载');
                return;
            }

            // 避免重复创建
            if (window.threeFallbackCreated) {
                console.log('Three.js替代方案已创建');
                return;
            }

            console.log('Three.js未加载，直接创建HTML替代方案');
            createSimpleThreeJSFallback();
        }

        // 创建简化的Three.js替代方案
        function createSimpleThreeJSFallback() {
            // 防止重复创建
            if (window.threeFallbackCreated) {
                console.log('Three.js替代方案已存在');
                return;
            }
            window.threeFallbackCreated = true;

            console.log('创建Three.js简化替代方案...');

            // 创建一个简化的THREE对象
            window.THREE = {
                Scene: function() {
                    return {
                        add: function() {},
                        remove: function() {}
                    };
                },
                PerspectiveCamera: function() {
                    return {
                        position: { set: function() {} },
                        lookAt: function() {}
                    };
                },
                WebGLRenderer: function() {
                    return {
                        setSize: function() {},
                        render: function() {},
                        domElement: document.createElement('canvas')
                    };
                },
                BoxGeometry: function() { return {}; },
                MeshBasicMaterial: function() { return {}; },
                Mesh: function() {
                    return {
                        rotation: { x: 0, y: 0, z: 0 },
                        position: { set: function() {} }
                    };
                },
                OrbitControls: function(camera, domElement) {
                    this.camera = camera;
                    this.domElement = domElement;
                    this.enableDamping = true;
                    this.dampingFactor = 0.05;
                    this.update = function() {};
                    this.reset = function() {
                        if (this.camera && this.camera.position) {
                            this.camera.position.set(0, 5, 10);
                            this.camera.lookAt(0, 0, 0);
                        }
                    };
                }
            };

            console.log('Three.js简化替代方案创建完成');
        }

        // 创建3D场景的HTML替代方案
        function create3DFallback(container) {
            container.innerHTML = `
                <div class="three-fallback-container">
                    <div class="fallback-header">
                        <div class="status-indicator offline"></div>
                        <span class="fallback-title">3D数据可视化中心</span>
                        <span class="fallback-mode">2D模拟模式</span>
                    </div>

                    <div class="fallback-content">
                        <div class="pseudo-3d-scene">
                            <div class="scene-grid">
                                <div class="grid-line horizontal" style="top: 20%;"></div>
                                <div class="grid-line horizontal" style="top: 40%;"></div>
                                <div class="grid-line horizontal" style="top: 60%;"></div>
                                <div class="grid-line horizontal" style="top: 80%;"></div>
                                <div class="grid-line vertical" style="left: 20%;"></div>
                                <div class="grid-line vertical" style="left: 40%;"></div>
                                <div class="grid-line vertical" style="left: 60%;"></div>
                                <div class="grid-line vertical" style="left: 80%;"></div>
                            </div>

                            <div class="data-cubes">
                                <div class="data-cube" style="left: 25%; top: 30%; --height: 60px; --color: #00ff88;">
                                    <div class="cube-label">项目A<br>92%</div>
                                </div>
                                <div class="data-cube" style="left: 45%; top: 45%; --height: 45px; --color: #ffff00;">
                                    <div class="cube-label">项目B<br>78%</div>
                                </div>
                                <div class="data-cube" style="left: 65%; top: 25%; --height: 70px; --color: #00ff88;">
                                    <div class="cube-label">项目C<br>95%</div>
                                </div>
                                <div class="data-cube" style="left: 35%; top: 60%; --height: 50px; --color: #ff6600;">
                                    <div class="cube-label">项目D<br>82%</div>
                                </div>
                            </div>

                            <div class="scene-info">
                                <div class="info-panel">
                                    <div class="info-title">数据概览</div>
                                    <div class="info-stats">
                                        <div class="stat-item">
                                            <span class="stat-label">平均完成率:</span>
                                            <span class="stat-value">86.8%</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">最高项目:</span>
                                            <span class="stat-value">项目C (95%)</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">需关注:</span>
                                            <span class="stat-value">项目B (78%)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="fallback-controls">
                            <button class="control-button" onclick="animate3DFallback()">
                                <i class="fas fa-play"></i> 动画演示
                            </button>
                            <button class="control-button" onclick="reset3DFallback()">
                                <i class="fas fa-refresh"></i> 重置视图
                            </button>
                            <button class="control-button" onclick="loadThreeJSFallback()">
                                <i class="fas fa-download"></i> 重试加载3D
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 启动动画
            setTimeout(() => animate3DFallback(), 1000);
        }

        // 3D替代方案的动画
        function animate3DFallback() {
            const cubes = document.querySelectorAll('.data-cube');
            cubes.forEach((cube, index) => {
                cube.style.animation = 'none';
                setTimeout(() => {
                    cube.style.animation = 'cubeFloat 3s ease-in-out infinite';
                    cube.style.animationDelay = `${index * 0.5}s`;
                }, 100);
            });
        }

        // 重置3D替代方案
        function reset3DFallback() {
            const cubes = document.querySelectorAll('.data-cube');
            cubes.forEach(cube => {
                cube.style.animation = 'none';
                cube.style.transform = 'translateY(0)';
            });
            setTimeout(() => animate3DFallback(), 500);
        }

        // 高级流程控制台功能
        function animateFlowChart() {
            console.log('启动智能流程演示...');
            const statusItems = document.querySelectorAll('.status-item');
            statusItems.forEach((item, index) => {
                setTimeout(() => {
                    item.style.background = 'rgba(0, 255, 136, 0.15)';
                    item.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        item.style.background = '';
                        item.style.transform = '';
                    }, 800);
                }, index * 300);
            });
        }

        function refreshFlowChart() {
            console.log('实时刷新流程数据...');
            const metrics = document.querySelectorAll('.metric-value');
            metrics.forEach(metric => {
                metric.style.animation = 'pulse 1s ease-in-out';
                setTimeout(() => {
                    metric.style.animation = '';
                }, 1000);
            });
        }

        function configureFlow() {
            console.log('打开高级配置面板...');
            alert('高级配置功能开发中...\n\n可配置项目:\n- 流程节点设置\n- 自动化规则\n- 通知策略\n- 权限管理');
        }

        function exportFlow() {
            console.log('导出流程数据...');
            const data = {
                timestamp: new Date().toISOString(),
                flowStatus: '68% 完成',
                activeNodes: '4/7',
                metrics: {
                    daysRemaining: 24,
                    teamMembers: 12,
                    riskItems: 3
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'flow-data-' + new Date().toISOString().split('T')[0] + '.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Chart.js备用加载
        function loadChartJSFallback() {
            // 直接使用内置图表引擎，避免CDN问题
            console.log('启动内置高性能图表引擎');
            setTimeout(createSimpleChartFallbacks, 500);
        }

        // 创建简单的HTML图表替代方案
        function createSimpleChartFallbacks() {
            // 防止重复创建
            if (window.chartFallbacksCreated) {
                console.log('HTML图表替代方案已存在，跳过创建');
                return;
            }

            console.log('正在创建HTML图表替代方案...');





            // 标记已创建，防止重复
            window.chartFallbacksCreated = true;
            console.log('HTML图表替代方案创建完成');
        }



        // 3D性能立体图初始化
        function initRadarChart() {
            const container = document.getElementById('radar-chart');
            if (!container) return;

            // 创建新颖的3D立体性能图
            container.innerHTML = `
                <div class="performance-cube-visualization">
                    <div class="cube-title-section">
                        <h3 class="cube-main-title">多维性能立体图</h3>
                        <p class="cube-subtitle">3D Performance Analysis</p>
                    </div>

                    <div class="cube-container">
                        <!-- 3D立方体框架 -->
                        <div class="cube-framework">
                            <div class="cube-face front">
                                <div class="face-grid">
                                    <div class="grid-line horizontal" style="--position: 20%;"></div>
                                    <div class="grid-line horizontal" style="--position: 40%;"></div>
                                    <div class="grid-line horizontal" style="--position: 60%;"></div>
                                    <div class="grid-line horizontal" style="--position: 80%;"></div>
                                    <div class="grid-line vertical" style="--position: 20%;"></div>
                                    <div class="grid-line vertical" style="--position: 40%;"></div>
                                    <div class="grid-line vertical" style="--position: 60%;"></div>
                                    <div class="grid-line vertical" style="--position: 80%;"></div>
                                </div>
                            </div>
                            <div class="cube-face back"></div>
                            <div class="cube-face left"></div>
                            <div class="cube-face right"></div>
                            <div class="cube-face top"></div>
                            <div class="cube-face bottom"></div>
                        </div>

                        <!-- 性能数据球体 -->
                        <div class="performance-spheres">
                            <div class="perf-sphere" data-metric="需求分析" data-value="87" style="--x: 20%; --y: 30%; --z: 40%; --size: 87;">
                                <div class="sphere-core"></div>
                                <div class="sphere-ring ring-1"></div>
                                <div class="sphere-ring ring-2"></div>
                                <div class="sphere-label">需求分析<br><span>87%</span></div>
                            </div>
                            <div class="perf-sphere" data-metric="设计开发" data-value="92" style="--x: 70%; --y: 20%; --z: 60%; --size: 92;">
                                <div class="sphere-core"></div>
                                <div class="sphere-ring ring-1"></div>
                                <div class="sphere-ring ring-2"></div>
                                <div class="sphere-label">设计开发<br><span>92%</span></div>
                            </div>
                            <div class="perf-sphere" data-metric="测试验证" data-value="78" style="--x: 80%; --y: 70%; --z: 30%; --size: 78;">
                                <div class="sphere-core"></div>
                                <div class="sphere-ring ring-1"></div>
                                <div class="sphere-ring ring-2"></div>
                                <div class="sphere-label">测试验证<br><span>78%</span></div>
                            </div>
                            <div class="perf-sphere" data-metric="部署实施" data-value="85" style="--x: 30%; --y: 80%; --z: 70%; --size: 85;">
                                <div class="sphere-core"></div>
                                <div class="sphere-ring ring-1"></div>
                                <div class="sphere-ring ring-2"></div>
                                <div class="sphere-label">部署实施<br><span>85%</span></div>
                            </div>
                            <div class="perf-sphere" data-metric="运维监控" data-value="90" style="--x: 60%; --y: 60%; --z: 80%; --size: 90;">
                                <div class="sphere-core"></div>
                                <div class="sphere-ring ring-1"></div>
                                <div class="sphere-ring ring-2"></div>
                                <div class="sphere-label">运维监控<br><span>90%</span></div>
                            </div>
                            <div class="perf-sphere" data-metric="用户反馈" data-value="88" style="--x: 40%; --y: 40%; --z: 50%; --size: 88;">
                                <div class="sphere-core"></div>
                                <div class="sphere-ring ring-1"></div>
                                <div class="sphere-ring ring-2"></div>
                                <div class="sphere-label">用户反馈<br><span>88%</span></div>
                            </div>
                        </div>

                        <!-- 连接线 -->
                        <div class="connection-lines">
                            <div class="connect-line" style="--start-x: 20%; --start-y: 30%; --end-x: 70%; --end-y: 20%;"></div>
                            <div class="connect-line" style="--start-x: 70%; --start-y: 20%; --end-x: 80%; --end-y: 70%;"></div>
                            <div class="connect-line" style="--start-x: 80%; --start-y: 70%; --end-x: 30%; --end-y: 80%;"></div>
                            <div class="connect-line" style="--start-x: 30%; --start-y: 80%; --end-x: 60%; --end-y: 60%;"></div>
                            <div class="connect-line" style="--start-x: 60%; --start-y: 60%; --end-x: 40%; --end-y: 40%;"></div>
                            <div class="connect-line" style="--start-x: 40%; --start-y: 40%; --end-x: 20%; --end-y: 30%;"></div>
                        </div>

                        <!-- 中心统计面板 -->
                        <div class="center-stats-panel">
                            <div class="stats-hologram">
                                <div class="hologram-value">86.7%</div>
                                <div class="hologram-label">综合性能</div>
                                <div class="hologram-trend">↗ +2.3%</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 启动3D动画
            setTimeout(() => {
                const spheres = container.querySelectorAll('.perf-sphere');
                spheres.forEach((sphere, index) => {
                    setTimeout(() => {
                        sphere.classList.add('animate-in');
                    }, index * 200);
                });
            }, 500);
        }

        // 流动网络图初始化
        function initHeatmap() {
            const container = document.getElementById('heatmap-chart');
            if (!container) return;

            // 创建新颖的流动网络图
            container.innerHTML = `
                <div class="flow-network-visualization">
                    <div class="network-title-section">
                        <h3 class="network-main-title">项目协作流动网络</h3>
                        <p class="network-subtitle">Project Collaboration Flow Network</p>
                    </div>

                    <div class="network-container">
                        <!-- 背景粒子系统 -->
                        <div class="particle-background">
                            ${Array.from({length: 50}, (_, i) => `
                                <div class="particle" style="
                                    --delay: ${Math.random() * 5}s;
                                    --duration: ${3 + Math.random() * 4}s;
                                    --x: ${Math.random() * 100}%;
                                    --y: ${Math.random() * 100}%;
                                    --size: ${2 + Math.random() * 4}px;
                                "></div>
                            `).join('')}
                        </div>

                        <!-- 主要节点 -->
                        <div class="network-nodes">
                            <!-- 中心核心节点 -->
                            <div class="network-node core-node" style="--x: 50%; --y: 50%;" data-node="core">
                                <div class="node-core">
                                    <div class="core-pulse"></div>
                                    <div class="core-ring ring-1"></div>
                                    <div class="core-ring ring-2"></div>
                                    <div class="core-ring ring-3"></div>
                                </div>
                                <div class="node-label">
                                    <span class="label-title">协作中心</span>
                                    <span class="label-value">100%</span>
                                </div>
                            </div>

                            <!-- 项目节点 -->
                            <div class="network-node project-node" style="--x: 20%; --y: 20%;" data-node="alpha">
                                <div class="node-sphere alpha-sphere">
                                    <div class="sphere-glow"></div>
                                    <div class="data-flow"></div>
                                </div>
                                <div class="node-label">
                                    <span class="label-title">Alpha项目</span>
                                    <span class="label-value">87%</span>
                                </div>
                            </div>

                            <div class="network-node project-node" style="--x: 80%; --y: 25%;" data-node="beta">
                                <div class="node-sphere beta-sphere">
                                    <div class="sphere-glow"></div>
                                    <div class="data-flow"></div>
                                </div>
                                <div class="node-label">
                                    <span class="label-title">Beta项目</span>
                                    <span class="label-value">92%</span>
                                </div>
                            </div>

                            <div class="network-node project-node" style="--x: 85%; --y: 75%;" data-node="gamma">
                                <div class="node-sphere gamma-sphere">
                                    <div class="sphere-glow"></div>
                                    <div class="data-flow"></div>
                                </div>
                                <div class="node-label">
                                    <span class="label-title">Gamma项目</span>
                                    <span class="label-value">78%</span>
                                </div>
                            </div>

                            <div class="network-node project-node" style="--x: 15%; --y: 80%;" data-node="delta">
                                <div class="node-sphere delta-sphere">
                                    <div class="sphere-glow"></div>
                                    <div class="data-flow"></div>
                                </div>
                                <div class="node-label">
                                    <span class="label-title">Delta项目</span>
                                    <span class="label-value">85%</span>
                                </div>
                            </div>

                            <!-- 功能节点 -->
                            <div class="network-node function-node" style="--x: 50%; --y: 15%;" data-node="design">
                                <div class="function-icon">🎨</div>
                                <div class="node-label">设计</div>
                            </div>

                            <div class="network-node function-node" style="--x: 85%; --y: 50%;" data-node="dev">
                                <div class="function-icon">⚡</div>
                                <div class="node-label">开发</div>
                            </div>

                            <div class="network-node function-node" style="--x: 50%; --y: 85%;" data-node="test">
                                <div class="function-icon">🔍</div>
                                <div class="node-label">测试</div>
                            </div>

                            <div class="network-node function-node" style="--x: 15%; --y: 50%;" data-node="ops">
                                <div class="function-icon">🚀</div>
                                <div class="node-label">运维</div>
                            </div>
                        </div>

                        <!-- 连接线和数据流 -->
                        <svg class="connection-svg" viewBox="0 0 100 100">
                            <defs>
                                <linearGradient id="flowGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#00ff88;stop-opacity:0" />
                                    <stop offset="50%" style="stop-color:#00ff88;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#00ccff;stop-opacity:0" />
                                </linearGradient>
                                <linearGradient id="flowGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#00ccff;stop-opacity:0" />
                                    <stop offset="50%" style="stop-color:#00ccff;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#0066ff;stop-opacity:0" />
                                </linearGradient>
                            </defs>

                            <!-- 主要连接线 -->
                            <path class="flow-path" d="M 20 20 Q 35 35 50 50" stroke="url(#flowGradient1)" stroke-width="0.5" fill="none">
                                <animate attributeName="stroke-dasharray" values="0,10;10,0;0,10" dur="2s" repeatCount="indefinite"/>
                            </path>
                            <path class="flow-path" d="M 80 25 Q 65 37.5 50 50" stroke="url(#flowGradient1)" stroke-width="0.5" fill="none">
                                <animate attributeName="stroke-dasharray" values="0,10;10,0;0,10" dur="2.5s" repeatCount="indefinite"/>
                            </path>
                            <path class="flow-path" d="M 85 75 Q 67.5 62.5 50 50" stroke="url(#flowGradient2)" stroke-width="0.5" fill="none">
                                <animate attributeName="stroke-dasharray" values="0,10;10,0;0,10" dur="3s" repeatCount="indefinite"/>
                            </path>
                            <path class="flow-path" d="M 15 80 Q 32.5 65 50 50" stroke="url(#flowGradient2)" stroke-width="0.5" fill="none">
                                <animate attributeName="stroke-dasharray" values="0,10;10,0;0,10" dur="2.2s" repeatCount="indefinite"/>
                            </path>

                            <!-- 功能节点连接 -->
                            <path class="flow-path secondary" d="M 50 15 L 50 50" stroke="url(#flowGradient1)" stroke-width="0.3" fill="none">
                                <animate attributeName="stroke-dasharray" values="0,5;5,0;0,5" dur="1.8s" repeatCount="indefinite"/>
                            </path>
                            <path class="flow-path secondary" d="M 85 50 L 50 50" stroke="url(#flowGradient1)" stroke-width="0.3" fill="none">
                                <animate attributeName="stroke-dasharray" values="0,5;5,0;0,5" dur="2.1s" repeatCount="indefinite"/>
                            </path>
                            <path class="flow-path secondary" d="M 50 85 L 50 50" stroke="url(#flowGradient2)" stroke-width="0.3" fill="none">
                                <animate attributeName="stroke-dasharray" values="0,5;5,0;0,5" dur="1.9s" repeatCount="indefinite"/>
                            </path>
                            <path class="flow-path secondary" d="M 15 50 L 50 50" stroke="url(#flowGradient2)" stroke-width="0.3" fill="none">
                                <animate attributeName="stroke-dasharray" values="0,5;5,0;0,5" dur="2.3s" repeatCount="indefinite"/>
                            </path>
                        </svg>

                        <!-- 数据流指示器 -->
                        <div class="flow-indicators">
                            <div class="flow-indicator" style="--start-x: 20%; --start-y: 20%; --end-x: 50%; --end-y: 50%; --delay: 0s;">
                                <div class="indicator-dot"></div>
                            </div>
                            <div class="flow-indicator" style="--start-x: 80%; --start-y: 25%; --end-x: 50%; --end-y: 50%; --delay: 0.5s;">
                                <div class="indicator-dot"></div>
                            </div>
                            <div class="flow-indicator" style="--start-x: 85%; --start-y: 75%; --end-x: 50%; --end-y: 50%; --delay: 1s;">
                                <div class="indicator-dot"></div>
                            </div>
                            <div class="flow-indicator" style="--start-x: 15%; --start-y: 80%; --end-x: 50%; --end-y: 50%; --delay: 1.5s;">
                                <div class="indicator-dot"></div>
                            </div>
                        </div>

                        <!-- 网络统计面板 -->
                        <div class="network-stats-panel">
                            <div class="stats-hologram">
                                <div class="hologram-title">网络状态</div>
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <span class="stat-value">4</span>
                                        <span class="stat-label">活跃项目</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">87.5%</span>
                                        <span class="stat-label">协作效率</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">156</span>
                                        <span class="stat-label">数据流量</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">98.2%</span>
                                        <span class="stat-label">网络健康</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 启动网络动画
            setTimeout(() => {
                const nodes = container.querySelectorAll('.network-node');
                nodes.forEach((node, index) => {
                    setTimeout(() => {
                        node.classList.add('animate-in');
                    }, index * 150);
                });
            }, 500);
        }



        // 项目概览图表初始化
        function initOverviewCharts() {
            initRadarChart();
            initHeatmap();
            initFlowChart();
            initProjectStatusList();
        }

        // 雷达图
        function initRadarChart() {
            const container = document.getElementById('radar-chart');
            if (!container) return;

            if (typeof Plotly === 'undefined') {
                createRadarChartFallback(container);
                return;
            }

            try {
                const data = [{
                    type: 'scatterpolar',
                    r: [87, 92, 78, 85, 90, 88],
                    theta: ['需求分析', '设计开发', '测试验证', '部署实施', '运维监控', '用户反馈'],
                    fill: 'toself',
                    name: '当前项目',
                    line: { color: '#00ff88' },
                    fillcolor: 'rgba(0, 255, 136, 0.3)'
                }, {
                    type: 'scatterpolar',
                    r: [80, 85, 75, 82, 88, 85],
                    theta: ['需求分析', '设计开发', '测试验证', '部署实施', '运维监控', '用户反馈'],
                    fill: 'toself',
                    name: '历史平均',
                    line: { color: '#0066cc' },
                    fillcolor: 'rgba(0, 102, 204, 0.2)'
                }];

                const layout = {
                    polar: {
                        radialaxis: {
                            visible: true,
                            range: [0, 100],
                            color: '#8b949e'
                        },
                        angularaxis: {
                            color: '#8b949e'
                        }
                    },
                    showlegend: true,
                    legend: {
                        font: { color: '#ffffff' },
                        x: 0.8,
                        y: 1
                    },
                    paper_bgcolor: 'transparent',
                    plot_bgcolor: 'transparent',
                    font: { color: '#ffffff' }
                };

                Plotly.newPlot('radar-chart', data, layout, {responsive: true, displayModeBar: false});
            } catch (error) {
                console.log('Plotly雷达图初始化失败，使用fallback:', error);
                createRadarChartFallback(container);
            }
        }

        function createRadarChartFallback(container) {
            const data = [
                {label: '需求分析', current: 87, average: 80},
                {label: '设计开发', current: 92, average: 85},
                {label: '测试验证', current: 78, average: 75},
                {label: '部署实施', current: 85, average: 82},
                {label: '运维监控', current: 90, average: 88},
                {label: '用户反馈', current: 88, average: 85}
            ];

            let html = `
                <div style="display: flex; flex-direction: column; height: 100%; padding: 10px;">
                    <div style="text-align: center; margin-bottom: 15px; color: #ffffff; font-weight: bold;">多维性能雷达图</div>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; flex: 1;">
            `;

            data.forEach(item => {
                html += `
                    <div style="background: rgba(255,255,255,0.05); border-radius: 8px; padding: 12px; border-left: 3px solid #00ff88;">
                        <div style="font-size: 12px; color: #ffffff; margin-bottom: 8px; font-weight: 500;">${item.label}</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                            <span style="font-size: 10px; color: #00ff88;">当前: ${item.current}%</span>
                            <span style="font-size: 10px; color: #0066cc;">平均: ${item.average}%</span>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); height: 6px; border-radius: 3px; overflow: hidden;">
                            <div style="height: 100%; background: linear-gradient(90deg, #00ff88, #0066cc); width: ${item.current}%; border-radius: 3px; transition: width 0.5s ease;"></div>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                    <div style="text-align: center; margin-top: 10px; font-size: 10px; color: #8b949e;">
                        <span style="color: #00ff88;">■</span> 当前项目
                        <span style="color: #0066cc; margin-left: 15px;">■</span> 历史平均
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // 热力图
        function initHeatmap() {
            const container = document.getElementById('heatmap-chart');
            if (!container) return;

            if (typeof Plotly === 'undefined') {
                createHeatmapFallback(container);
                return;
            }

            try {
                const projects = ['项目A', '项目B', '项目C', '项目D', '项目E'];
                const weeks = ['第1周', '第2周', '第3周', '第4周', '第5周', '第6周'];
                const z = [
                    [85, 90, 88, 92, 87, 89],
                    [78, 82, 85, 88, 90, 92],
                    [92, 89, 91, 88, 85, 87],
                    [75, 78, 82, 85, 88, 90],
                    [88, 85, 87, 90, 92, 94]
                ];

                const data = [{
                    z: z,
                    x: weeks,
                    y: projects,
                    type: 'heatmap',
                    colorscale: [
                        [0, '#ff6600'],
                        [0.5, '#ffff00'],
                        [1, '#00ff88']
                    ],
                    showscale: true,
                    colorbar: {
                        title: '达成率(%)',
                        titlefont: { color: '#ffffff' },
                        tickfont: { color: '#ffffff' }
                    }
                }];

                const layout = {
                    title: {
                        text: '项目周度达成率',
                        font: { color: '#ffffff' }
                    },
                    xaxis: {
                        title: '时间周期',
                        color: '#8b949e'
                    },
                    yaxis: {
                        title: '项目名称',
                        color: '#8b949e'
                    },
                    paper_bgcolor: 'transparent',
                    plot_bgcolor: 'transparent',
                    font: { color: '#ffffff' }
                };

                Plotly.newPlot('heatmap-chart', data, layout, {responsive: true, displayModeBar: false});
            } catch (error) {
                console.log('Plotly热力图初始化失败，使用fallback:', error);
                createHeatmapFallback(container);
            }
        }

        function createHeatmapFallback(container) {
            const projects = ['项目A', '项目B', '项目C', '项目D', '项目E'];
            const weeks = ['第1周', '第2周', '第3周', '第4周', '第5周', '第6周'];
            const data = [
                [85, 90, 88, 92, 87, 89],
                [78, 82, 85, 88, 90, 92],
                [92, 89, 91, 88, 85, 87],
                [75, 78, 82, 85, 88, 90],
                [88, 85, 87, 90, 92, 94]
            ];

            function getHeatColor(value) {
                if (value >= 90) return '#00ff88';
                if (value >= 80) return '#ffff00';
                return '#ff6600';
            }

            let html = `
                <div style="padding: 10px; height: 100%;">
                    <div style="text-align: center; margin-bottom: 15px; color: #ffffff; font-weight: bold; font-size: 14px;">项目周度达成率热力图</div>
                    <div style="display: grid; grid-template-columns: 80px repeat(6, 1fr); gap: 2px; font-size: 10px;">
                        <div></div>
            `;

            // 添加周标题
            weeks.forEach(week => {
                html += `<div style="text-align: center; color: #8b949e; padding: 4px; font-weight: 500;">${week}</div>`;
            });

            // 添加项目数据
            projects.forEach((project, i) => {
                html += `<div style="color: #8b949e; padding: 4px; font-weight: 500; text-align: right;">${project}</div>`;
                data[i].forEach(value => {
                    const color = getHeatColor(value);
                    html += `
                        <div style="
                            background: ${color};
                            color: #000;
                            text-align: center;
                            padding: 8px 4px;
                            border-radius: 3px;
                            font-weight: bold;
                            transition: all 0.3s ease;
                            cursor: pointer;
                        "
                        onmouseover="this.style.transform='scale(1.1)'; this.style.zIndex='10';"
                        onmouseout="this.style.transform='scale(1)'; this.style.zIndex='1';"
                        title="达成率: ${value}%">${value}%</div>
                    `;
                });
            });

            html += `
                    </div>
                    <div style="display: flex; justify-content: center; margin-top: 15px; gap: 15px; font-size: 10px;">
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <div style="width: 12px; height: 12px; background: #ff6600; border-radius: 2px;"></div>
                            <span style="color: #8b949e;">低于80%</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <div style="width: 12px; height: 12px; background: #ffff00; border-radius: 2px;"></div>
                            <span style="color: #8b949e;">80-90%</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <div style="width: 12px; height: 12px; background: #00ff88; border-radius: 2px;"></div>
                            <span style="color: #8b949e;">90%以上</span>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // 流程图
        function initFlowChart() {
            const container = document.getElementById('flow-chart');
            if (!container) return;

            if (typeof d3 === 'undefined') {
                createFlowChartFallback(container);
                return;
            }

            try {
                // 清空容器
                container.innerHTML = '';

                const svg = d3.select('#flow-chart')
                    .append('svg')
                    .attr('width', '100%')
                    .attr('height', '100%')
                    .attr('viewBox', '0 0 1000 250');

                const nodes = [
                    {id: 'start', x: 80, y: 125, label: '项目启动', status: 'completed'},
                    {id: 'planning', x: 220, y: 125, label: '规划设计', status: 'completed'},
                    {id: 'development', x: 360, y: 125, label: '开发实施', status: 'active'},
                    {id: 'testing', x: 500, y: 125, label: '测试验证', status: 'pending'},
                    {id: 'deployment', x: 640, y: 125, label: '部署上线', status: 'pending'},
                    {id: 'maintenance', x: 780, y: 125, label: '运维监控', status: 'pending'},
                    {id: 'milestone1', x: 220, y: 70, label: '里程碑1', status: 'completed'},
                    {id: 'milestone2', x: 360, y: 70, label: '里程碑2', status: 'active'},
                    {id: 'milestone3', x: 500, y: 70, label: '里程碑3', status: 'pending'},
                    {id: 'milestone4', x: 640, y: 70, label: '里程碑4', status: 'pending'}
                ];

                const links = [
                    {source: 'start', target: 'planning'},
                    {source: 'planning', target: 'development'},
                    {source: 'development', target: 'testing'},
                    {source: 'testing', target: 'deployment'},
                    {source: 'deployment', target: 'maintenance'},
                    {source: 'planning', target: 'milestone1'},
                    {source: 'development', target: 'milestone2'},
                    {source: 'testing', target: 'milestone3'},
                    {source: 'deployment', target: 'milestone4'}
                ];

                // 绘制连接线
                svg.selectAll('.link')
                    .data(links)
                    .enter()
                    .append('line')
                    .attr('class', 'link')
                    .attr('x1', d => nodes.find(n => n.id === d.source).x)
                    .attr('y1', d => nodes.find(n => n.id === d.source).y)
                    .attr('x2', d => nodes.find(n => n.id === d.target).x)
                    .attr('y2', d => nodes.find(n => n.id === d.target).y)
                    .attr('stroke', '#30363d')
                    .attr('stroke-width', 2);

                // 绘制节点
                const nodeGroups = svg.selectAll('.node')
                    .data(nodes)
                    .enter()
                    .append('g')
                    .attr('class', 'node')
                    .attr('transform', d => `translate(${d.x}, ${d.y})`);

                nodeGroups.append('circle')
                    .attr('r', 25)
                    .attr('fill', d => {
                        switch(d.status) {
                            case 'completed': return '#00ff88';
                            case 'active': return '#ff6600';
                            case 'pending': return '#8b949e';
                            default: return '#8b949e';
                        }
                    })
                    .attr('stroke', '#ffffff')
                    .attr('stroke-width', 2);

                nodeGroups.append('text')
                    .attr('text-anchor', 'middle')
                    .attr('dy', 40)
                    .attr('fill', '#ffffff')
                    .attr('font-size', '12px')
                    .text(d => d.label);
            } catch (error) {
                console.log('D3流程图初始化失败，使用fallback:', error);
                createFlowChartFallback(container);
            }
        }

        function createFlowChartFallback(container) {
            const nodes = [
                {label: '项目启动', status: 'completed', progress: 100},
                {label: '规划设计', status: 'completed', progress: 100},
                {label: '开发实施', status: 'active', progress: 75},
                {label: '测试验证', status: 'pending', progress: 0},
                {label: '部署上线', status: 'pending', progress: 0},
                {label: '运维监控', status: 'pending', progress: 0}
            ];

            const milestones = [
                {label: '里程碑1', status: 'completed'},
                {label: '里程碑2', status: 'active'},
                {label: '里程碑3', status: 'pending'},
                {label: '里程碑4', status: 'pending'}
            ];

            function getStatusColor(status) {
                switch(status) {
                    case 'completed': return '#00ff88';
                    case 'active': return '#ff6600';
                    case 'pending': return '#8b949e';
                    default: return '#8b949e';
                }
            }

            function getStatusIcon(status) {
                switch(status) {
                    case 'completed': return '✓';
                    case 'active': return '⚡';
                    case 'pending': return '○';
                    default: return '○';
                }
            }

            let html = `
                <div style="padding: 15px; height: 100%; display: flex; flex-direction: column;">
                    <div style="text-align: center; margin-bottom: 20px; color: #ffffff; font-weight: bold; font-size: 14px;">项目流程图</div>

                    <!-- 主流程 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; position: relative;">
                        <!-- 连接线 -->
                        <div style="position: absolute; top: 50%; left: 0; right: 0; height: 2px; background: linear-gradient(90deg, #00ff88 0%, #ff6600 50%, #8b949e 100%); z-index: 1;"></div>
            `;

            nodes.forEach((node, index) => {
                const color = getStatusColor(node.status);
                const icon = getStatusIcon(node.status);

                html += `
                    <div style="
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        z-index: 2;
                        background: var(--bg-dark);
                        padding: 5px;
                        border-radius: 8px;
                        min-width: 80px;
                    ">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: ${color};
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #000;
                            font-weight: bold;
                            font-size: 18px;
                            border: 2px solid #ffffff;
                            margin-bottom: 8px;
                        ">${icon}</div>
                        <div style="font-size: 10px; color: #ffffff; text-align: center; font-weight: 500;">${node.label}</div>
                        <div style="font-size: 8px; color: ${color}; margin-top: 2px;">${node.progress}%</div>
                    </div>
                `;
            });

            html += `
                    </div>

                    <!-- 里程碑 -->
                    <div style="margin-top: 10px;">
                        <div style="font-size: 12px; color: #8b949e; margin-bottom: 10px; text-align: center;">关键里程碑</div>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px;">
            `;

            milestones.forEach(milestone => {
                const color = getStatusColor(milestone.status);
                const icon = getStatusIcon(milestone.status);

                html += `
                    <div style="
                        background: rgba(255,255,255,0.05);
                        border: 1px solid ${color};
                        border-radius: 6px;
                        padding: 8px;
                        text-align: center;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(255,255,255,0.1)'" onmouseout="this.style.background='rgba(255,255,255,0.05)'">
                        <div style="color: ${color}; font-size: 16px; margin-bottom: 4px;">${icon}</div>
                        <div style="font-size: 9px; color: #ffffff;">${milestone.label}</div>
                    </div>
                `;
            });

            html += `
                        </div>
                    </div>

                    <!-- 状态说明 -->
                    <div style="display: flex; justify-content: center; margin-top: 15px; gap: 15px; font-size: 9px;">
                        <div style="display: flex; align-items: center; gap: 4px;">
                            <div style="width: 8px; height: 8px; background: #00ff88; border-radius: 50%;"></div>
                            <span style="color: #8b949e;">已完成</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 4px;">
                            <div style="width: 8px; height: 8px; background: #ff6600; border-radius: 50%;"></div>
                            <span style="color: #8b949e;">进行中</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 4px;">
                            <div style="width: 8px; height: 8px; background: #8b949e; border-radius: 50%;"></div>
                            <span style="color: #8b949e;">待开始</span>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // 项目状态列表
        function initProjectStatusList() {
            const projects = [
                {name: 'ERP系统升级', progress: 87, status: 'active', risk: 'low'},
                {name: '移动端应用开发', progress: 92, status: 'active', risk: 'low'},
                {name: '数据中心迁移', progress: 45, status: 'active', risk: 'high'},
                {name: '安全系统改造', progress: 78, status: 'active', risk: 'medium'},
                {name: '客户服务平台', progress: 95, status: 'completed', risk: 'low'},
                {name: '供应链管理系统', progress: 23, status: 'active', risk: 'high'}
            ];

            const container = document.getElementById('project-status-list');
            container.innerHTML = '';

            projects.forEach(project => {
                const projectDiv = document.createElement('div');
                projectDiv.className = 'project-item mb-2';
                projectDiv.style.cssText = `
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                    border-left: 3px solid ${getRiskColor(project.risk)};
                    padding: 12px;
                    transition: all 0.3s ease;
                    cursor: pointer;
                `;

                projectDiv.addEventListener('mouseenter', () => {
                    projectDiv.style.background = 'rgba(255, 255, 255, 0.1)';
                    projectDiv.style.transform = 'translateX(2px)';
                });

                projectDiv.addEventListener('mouseleave', () => {
                    projectDiv.style.background = 'rgba(255, 255, 255, 0.05)';
                    projectDiv.style.transform = 'translateX(0)';
                });

                projectDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold" style="font-size: 0.9rem;">${project.name}</span>
                        <span class="badge ${getStatusBadge(project.status)}" style="font-size: 0.7rem;">${getStatusText(project.status)}</span>
                    </div>
                    <div class="progress mb-2" style="height: 4px; background: rgba(255,255,255,0.1);">
                        <div class="progress-bar" style="width: ${project.progress}%; background: ${getProgressColor(project.progress)}; transition: width 0.5s ease;"></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small class="text-muted" style="font-size: 0.75rem;">
                            <i class="fas fa-chart-line" style="width: 12px;"></i> ${project.progress}%
                        </small>
                        <small class="text-muted" style="font-size: 0.75rem;">
                            <i class="fas fa-shield-alt" style="width: 12px; color: ${getRiskColor(project.risk)};"></i> ${getRiskText(project.risk)}
                        </small>
                    </div>
                `;

                container.appendChild(projectDiv);
            });
        }

        // 辅助函数
        function getRiskColor(risk) {
            switch(risk) {
                case 'low': return '#00ff88';
                case 'medium': return '#ffff00';
                case 'high': return '#ff6600';
                default: return '#8b949e';
            }
        }

        function getStatusBadge(status) {
            switch(status) {
                case 'active': return 'bg-primary';
                case 'completed': return 'bg-success';
                case 'pending': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'active': return '进行中';
                case 'completed': return '已完成';
                case 'pending': return '待开始';
                default: return '未知';
            }
        }

        function getProgressColor(progress) {
            if (progress >= 80) return '#00ff88';
            if (progress >= 60) return '#ffff00';
            return '#ff6600';
        }

        function getRiskText(risk) {
            switch(risk) {
                case 'low': return '低';
                case 'medium': return '中';
                case 'high': return '高';
                default: return '未知';
            }
        }

        // 刷新功能
        function refreshRadarChart() {
            // 模拟数据更新
            setTimeout(() => {
                initRadarChart();
                // 确保雷达图显示
                setTimeout(() => {
                    const radarChart = document.getElementById('radar-chart');
                    if (radarChart && (radarChart.innerHTML.trim() === '' || radarChart.innerHTML.includes('图表库加载中'))) {
                        createRadarChartFallback(radarChart);
                    }
                }, 200);
            }, 500);
        }

        function refreshHeatmap() {
            // 模拟数据更新
            setTimeout(() => {
                initHeatmap();
                // 确保热力图显示
                setTimeout(() => {
                    const heatmapChart = document.getElementById('heatmap-chart');
                    if (heatmapChart && (heatmapChart.innerHTML.trim() === '' || heatmapChart.innerHTML.includes('图表库加载中'))) {
                        createHeatmapFallback(heatmapChart);
                    }
                }, 200);
            }, 500);
        }

        function animateFlowChart() {
            // 流程图动画效果
            if (typeof d3 !== 'undefined') {
                const svg = d3.select('#flow-chart svg');
                if (svg.node()) {
                    svg.selectAll('.node circle')
                        .transition()
                        .duration(1000)
                        .attr('r', 35)
                        .transition()
                        .duration(1000)
                        .attr('r', 25);
                }
            }
        }

        // 新增的辅助函数
        function refreshProjectStatus() {
            initProjectStatusList();
        }

        function refreshFlowChart() {
            initFlowChart();
        }

        function exportChart(chartType) {
            alert(`导出${chartType}图表功能 - 这里可以实现图表导出为PNG/PDF`);
        }

        // 里程碑追踪功能
        function initMilestonesCharts() {
            initMilestoneTimeline();
            initMilestoneList();
            initMilestoneStats();
            initUpcomingMilestones();
            initRiskAssessment();
        }

        // 里程碑时间线
        function initMilestoneTimeline() {
            const container = document.getElementById('milestone-timeline');
            container.innerHTML = '';

            const milestones = [
                {id: 1, name: '需求确认', date: '2024-01-15', status: 'completed', progress: 100},
                {id: 2, name: '设计评审', date: '2024-02-01', status: 'completed', progress: 100},
                {id: 3, name: '开发完成', date: '2024-03-15', status: 'active', progress: 75},
                {id: 4, name: '测试完成', date: '2024-04-01', status: 'pending', progress: 0},
                {id: 5, name: '部署上线', date: '2024-04-15', status: 'pending', progress: 0}
            ];

            const timelineDiv = document.createElement('div');
            timelineDiv.style.cssText = `
                display: flex;
                align-items: center;
                padding: 20px;
                min-width: 800px;
                position: relative;
            `;

            // 绘制时间线
            const line = document.createElement('div');
            line.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50px;
                right: 50px;
                height: 2px;
                background: linear-gradient(90deg, #00ff88 0%, #00ff88 60%, #8b949e 60%, #8b949e 100%);
                z-index: 1;
            `;
            timelineDiv.appendChild(line);

            milestones.forEach((milestone, index) => {
                const milestoneDiv = document.createElement('div');
                milestoneDiv.style.cssText = `
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-right: ${index < milestones.length - 1 ? '150px' : '0'};
                    position: relative;
                    z-index: 2;
                `;

                const circle = document.createElement('div');
                circle.style.cssText = `
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: ${getMilestoneColor(milestone.status)};
                    border: 3px solid #ffffff;
                    margin-bottom: 10px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                `;

                circle.addEventListener('click', () => showMilestoneDetails(milestone));
                circle.addEventListener('mouseenter', () => {
                    circle.style.transform = 'scale(1.2)';
                });
                circle.addEventListener('mouseleave', () => {
                    circle.style.transform = 'scale(1)';
                });

                const label = document.createElement('div');
                label.style.cssText = `
                    text-align: center;
                    font-size: 0.9rem;
                    color: #ffffff;
                    font-weight: 600;
                    max-width: 100px;
                    background: rgba(0, 0, 0, 0.7);
                    padding: 4px 8px;
                    border-radius: 4px;
                    border: 1px solid rgba(0, 255, 136, 0.3);
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                `;
                label.textContent = milestone.name;

                const date = document.createElement('div');
                date.style.cssText = `
                    text-align: center;
                    font-size: 0.8rem;
                    color: #00ff88;
                    font-weight: 500;
                    margin-top: 8px;
                    background: rgba(0, 0, 0, 0.6);
                    padding: 2px 6px;
                    border-radius: 3px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                    font-family: 'Orbitron', monospace;
                `;
                date.textContent = milestone.date;

                milestoneDiv.appendChild(circle);
                milestoneDiv.appendChild(label);
                milestoneDiv.appendChild(date);
                timelineDiv.appendChild(milestoneDiv);
            });

            container.appendChild(timelineDiv);
        }

        // 里程碑详细列表
        function initMilestoneList() {
            const milestones = [
                {
                    id: 1,
                    name: '需求分析与确认',
                    project: 'ERP系统升级',
                    startDate: '2024-01-01',
                    endDate: '2024-01-15',
                    status: 'completed',
                    progress: 100,
                    responsible: '张三',
                    description: '完成系统需求收集、分析和确认工作'
                },
                {
                    id: 2,
                    name: '系统设计评审',
                    project: 'ERP系统升级',
                    startDate: '2024-01-16',
                    endDate: '2024-02-01',
                    status: 'completed',
                    progress: 100,
                    responsible: '李四',
                    description: '完成系统架构设计和技术方案评审'
                },
                {
                    id: 3,
                    name: '核心模块开发',
                    project: 'ERP系统升级',
                    startDate: '2024-02-02',
                    endDate: '2024-03-15',
                    status: 'active',
                    progress: 75,
                    responsible: '王五',
                    description: '开发系统核心业务模块'
                },
                {
                    id: 4,
                    name: '系统集成测试',
                    project: 'ERP系统升级',
                    startDate: '2024-03-16',
                    endDate: '2024-04-01',
                    status: 'pending',
                    progress: 0,
                    responsible: '赵六',
                    description: '进行系统集成和功能测试'
                }
            ];

            const container = document.getElementById('milestone-list');
            container.innerHTML = '';

            milestones.forEach(milestone => {
                const milestoneCard = document.createElement('div');
                milestoneCard.className = 'milestone-card mb-3 p-3';
                milestoneCard.style.cssText = `
                    background: linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4));
                    border-radius: 10px;
                    border: 1px solid rgba(0, 255, 136, 0.2);
                    border-left: 4px solid ${getMilestoneColor(milestone.status)};
                    cursor: pointer;
                    transition: all 0.3s ease;
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
                `;

                milestoneCard.addEventListener('mouseenter', () => {
                    milestoneCard.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.15), rgba(0, 0, 0, 0.5))';
                    milestoneCard.style.transform = 'translateY(-2px)';
                    milestoneCard.style.boxShadow = '0 6px 20px rgba(0, 255, 136, 0.2)';
                });
                milestoneCard.addEventListener('mouseleave', () => {
                    milestoneCard.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4))';
                    milestoneCard.style.transform = 'translateY(0)';
                    milestoneCard.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.3)';
                });

                milestoneCard.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <h6 class="mb-1" style="color: #ffffff; font-weight: 600; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);">${milestone.name}</h6>
                            <small style="color: #00ff88; font-weight: 500;">${milestone.project}</small>
                        </div>
                        <span class="badge ${getMilestoneStatusBadge(milestone.status)}" style="font-weight: 600; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);">${getMilestoneStatusText(milestone.status)}</span>
                    </div>
                    <div class="mb-2">
                        <div class="d-flex justify-content-between mb-1">
                            <small style="color: #ffffff; font-weight: 500;">进度</small>
                            <small style="color: #00ff88; font-weight: 600; font-family: 'Orbitron', monospace;">${milestone.progress}%</small>
                        </div>
                        <div class="progress" style="height: 8px; background: rgba(0, 0, 0, 0.3); border-radius: 4px;">
                            <div class="progress-bar" style="width: ${milestone.progress}%; background: ${getMilestoneColor(milestone.status)}; border-radius: 4px; box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);"></div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small style="color: #ffffff; opacity: 0.9; font-weight: 500;">
                            <i class="fas fa-calendar" style="color: #00ff88;"></i> ${milestone.startDate} - ${milestone.endDate}
                        </small>
                        <small style="color: #ffffff; opacity: 0.9; font-weight: 500;">
                            <i class="fas fa-user" style="color: #00ff88;"></i> ${milestone.responsible}
                        </small>
                    </div>
                `;

                milestoneCard.addEventListener('click', () => showMilestoneDetails(milestone));
                container.appendChild(milestoneCard);
            });
        }

        // 里程碑统计图表 - 高级3D甜甜圈图
        function initMilestoneStats() {
            const container = document.getElementById('milestone-stats-chart');
            container.innerHTML = '';

            // 创建高级统计图表容器
            const chartWrapper = document.createElement('div');
            chartWrapper.style.cssText = `
                position: relative;
                width: 100%;
                height: 250px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: radial-gradient(circle at center, rgba(0, 255, 136, 0.1), transparent);
            `;

            // 创建SVG容器
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', '220');
            svg.setAttribute('height', '220');
            svg.setAttribute('viewBox', '0 0 220 220');
            svg.style.cssText = `
                filter: drop-shadow(0 0 20px rgba(0, 255, 136, 0.3));
            `;

            const data = [
                { label: '已完成', value: 8, color: '#00ff88', percentage: 57.1 },
                { label: '进行中', value: 3, color: '#ff6600', percentage: 21.4 },
                { label: '待开始', value: 2, color: '#8b949e', percentage: 14.3 },
                { label: '延期', value: 1, color: '#ff4444', percentage: 7.1 }
            ];

            const total = data.reduce((sum, item) => sum + item.value, 0);
            const centerX = 110;
            const centerY = 110;
            const outerRadius = 80;
            const innerRadius = 45;

            let currentAngle = -90; // 从顶部开始

            // 创建渐变定义
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            data.forEach((item, index) => {
                const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'radialGradient');
                gradient.setAttribute('id', `gradient-${index}`);
                gradient.setAttribute('cx', '30%');
                gradient.setAttribute('cy', '30%');

                const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
                stop1.setAttribute('offset', '0%');
                stop1.setAttribute('stop-color', item.color);
                stop1.setAttribute('stop-opacity', '1');

                const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
                stop2.setAttribute('offset', '100%');
                stop2.setAttribute('stop-color', item.color);
                stop2.setAttribute('stop-opacity', '0.6');

                gradient.appendChild(stop1);
                gradient.appendChild(stop2);
                defs.appendChild(gradient);
            });
            svg.appendChild(defs);

            // 绘制甜甜圈片段
            data.forEach((item, index) => {
                const angle = (item.value / total) * 360;
                const startAngle = currentAngle;
                const endAngle = currentAngle + angle;

                // 计算路径
                const startAngleRad = (startAngle * Math.PI) / 180;
                const endAngleRad = (endAngle * Math.PI) / 180;

                const x1 = centerX + outerRadius * Math.cos(startAngleRad);
                const y1 = centerY + outerRadius * Math.sin(startAngleRad);
                const x2 = centerX + outerRadius * Math.cos(endAngleRad);
                const y2 = centerY + outerRadius * Math.sin(endAngleRad);

                const x3 = centerX + innerRadius * Math.cos(endAngleRad);
                const y3 = centerY + innerRadius * Math.sin(endAngleRad);
                const x4 = centerX + innerRadius * Math.cos(startAngleRad);
                const y4 = centerY + innerRadius * Math.sin(startAngleRad);

                const largeArcFlag = angle > 180 ? 1 : 0;

                const pathData = [
                    `M ${x1} ${y1}`,
                    `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                    `L ${x3} ${y3}`,
                    `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}`,
                    'Z'
                ].join(' ');

                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', pathData);
                path.setAttribute('fill', `url(#gradient-${index})`);
                path.setAttribute('stroke', 'rgba(255, 255, 255, 0.1)');
                path.setAttribute('stroke-width', '1');
                path.style.cssText = `
                    cursor: pointer;
                    transition: all 0.3s ease;
                    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
                `;

                // 添加悬停效果
                path.addEventListener('mouseenter', () => {
                    path.style.transform = 'scale(1.05)';
                    path.style.transformOrigin = `${centerX}px ${centerY}px`;
                    path.style.filter = `drop-shadow(0 4px 15px ${item.color}40)`;
                });

                path.addEventListener('mouseleave', () => {
                    path.style.transform = 'scale(1)';
                    path.style.filter = 'drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3))';
                });

                svg.appendChild(path);
                currentAngle += angle;
            });

            // 中心文字
            const centerGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');

            const totalText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            totalText.setAttribute('x', centerX);
            totalText.setAttribute('y', centerY - 5);
            totalText.setAttribute('text-anchor', 'middle');
            totalText.setAttribute('fill', '#ffffff');
            totalText.setAttribute('font-size', '24');
            totalText.setAttribute('font-weight', '700');
            totalText.setAttribute('font-family', 'Orbitron, monospace');
            totalText.textContent = total;

            const labelText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            labelText.setAttribute('x', centerX);
            labelText.setAttribute('y', centerY + 15);
            labelText.setAttribute('text-anchor', 'middle');
            labelText.setAttribute('fill', '#00ff88');
            labelText.setAttribute('font-size', '12');
            labelText.setAttribute('font-weight', '600');
            labelText.textContent = '总里程碑';

            centerGroup.appendChild(totalText);
            centerGroup.appendChild(labelText);
            svg.appendChild(centerGroup);

            chartWrapper.appendChild(svg);

            // 创建图例
            const legend = document.createElement('div');
            legend.style.cssText = `
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                flex-direction: column;
                gap: 8px;
            `;

            data.forEach(item => {
                const legendItem = document.createElement('div');
                legendItem.style.cssText = `
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 0.8rem;
                    color: #ffffff;
                    font-weight: 500;
                `;

                const colorBox = document.createElement('div');
                colorBox.style.cssText = `
                    width: 12px;
                    height: 12px;
                    background: ${item.color};
                    border-radius: 2px;
                    box-shadow: 0 0 8px ${item.color}40;
                `;

                const label = document.createElement('span');
                label.textContent = item.label;

                const value = document.createElement('span');
                value.style.cssText = `
                    color: ${item.color};
                    font-weight: 700;
                    font-family: 'Orbitron', monospace;
                    margin-left: auto;
                `;
                value.textContent = `${item.percentage}%`;

                legendItem.appendChild(colorBox);
                legendItem.appendChild(label);
                legendItem.appendChild(value);
                legend.appendChild(legendItem);
            });

            chartWrapper.appendChild(legend);
            container.appendChild(chartWrapper);
        }

        // 即将到期的里程碑
        function initUpcomingMilestones() {
            const upcomingMilestones = [
                {name: '核心模块开发', daysLeft: 5, project: 'ERP系统升级'},
                {name: '用户界面设计', daysLeft: 12, project: '移动端应用'},
                {name: '数据迁移测试', daysLeft: 18, project: '数据中心迁移'},
                {name: '安全审计', daysLeft: 25, project: '安全系统改造'}
            ];

            const container = document.getElementById('upcoming-milestones');
            container.innerHTML = '';

            upcomingMilestones.forEach(milestone => {
                const milestoneDiv = document.createElement('div');
                milestoneDiv.className = 'upcoming-milestone mb-2 p-2';
                milestoneDiv.style.cssText = `
                    background: linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4));
                    border-radius: 6px;
                    border: 1px solid rgba(0, 255, 136, 0.2);
                    border-left: 3px solid ${milestone.daysLeft <= 7 ? '#ff6600' : '#00ff88'};
                    transition: all 0.3s ease;
                    cursor: pointer;
                `;

                milestoneDiv.addEventListener('mouseenter', () => {
                    milestoneDiv.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.15), rgba(0, 0, 0, 0.5))';
                    milestoneDiv.style.transform = 'translateX(5px)';
                });
                milestoneDiv.addEventListener('mouseleave', () => {
                    milestoneDiv.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4))';
                    milestoneDiv.style.transform = 'translateX(0)';
                });

                milestoneDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div style="font-size: 0.9rem; font-weight: 600; color: #ffffff; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);">${milestone.name}</div>
                            <small style="color: #00ff88; font-weight: 500;">${milestone.project}</small>
                        </div>
                        <div class="text-end">
                            <div style="font-weight: 700; color: ${milestone.daysLeft <= 7 ? '#ff6600' : '#00ff88'}; font-family: 'Orbitron', monospace; font-size: 1rem; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);">
                                ${milestone.daysLeft}天
                            </div>
                            <small style="color: #ffffff; opacity: 0.8; font-weight: 500;">剩余</small>
                        </div>
                    </div>
                `;

                container.appendChild(milestoneDiv);
            });
        }

        // 里程碑风险评估
        function initRiskAssessment() {
            const container = document.getElementById('risk-assessment');
            container.innerHTML = '';

            const riskData = [
                {
                    project: 'ERP系统升级',
                    milestone: '核心模块开发',
                    riskLevel: 'high',
                    riskScore: 8.5,
                    factors: ['资源不足', '技术难度高'],
                    daysLeft: 5,
                    probability: 85
                },
                {
                    project: '移动端应用',
                    milestone: '用户界面设计',
                    riskLevel: 'medium',
                    riskScore: 6.2,
                    factors: ['需求变更', '设计复杂'],
                    daysLeft: 12,
                    probability: 62
                },
                {
                    project: '数据中心迁移',
                    milestone: '数据迁移测试',
                    riskLevel: 'low',
                    riskScore: 3.8,
                    factors: ['进度正常'],
                    daysLeft: 18,
                    probability: 38
                }
            ];

            // 创建风险概览
            const overviewDiv = document.createElement('div');
            overviewDiv.style.cssText = `
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
                margin-bottom: 20px;
            `;

            const riskLevels = [
                { level: 'high', count: 1, color: '#ff4444', label: '高风险' },
                { level: 'medium', count: 1, color: '#ff6600', label: '中风险' },
                { level: 'low', count: 1, color: '#00ff88', label: '低风险' }
            ];

            riskLevels.forEach(risk => {
                const riskCard = document.createElement('div');
                riskCard.style.cssText = `
                    background: linear-gradient(135deg, ${risk.color}20, rgba(0, 0, 0, 0.4));
                    border: 1px solid ${risk.color}40;
                    border-radius: 8px;
                    padding: 15px;
                    text-align: center;
                    transition: all 0.3s ease;
                    cursor: pointer;
                `;

                riskCard.addEventListener('mouseenter', () => {
                    riskCard.style.transform = 'translateY(-2px)';
                    riskCard.style.boxShadow = `0 8px 25px ${risk.color}30`;
                });

                riskCard.addEventListener('mouseleave', () => {
                    riskCard.style.transform = 'translateY(0)';
                    riskCard.style.boxShadow = 'none';
                });

                riskCard.innerHTML = `
                    <div style="font-size: 2rem; font-weight: 700; color: ${risk.color}; font-family: 'Orbitron', monospace; margin-bottom: 5px;">${risk.count}</div>
                    <div style="color: #ffffff; font-weight: 600; font-size: 0.9rem;">${risk.label}</div>
                `;

                overviewDiv.appendChild(riskCard);
            });

            container.appendChild(overviewDiv);

            // 创建详细风险列表
            const riskListDiv = document.createElement('div');
            riskListDiv.style.cssText = `
                display: flex;
                flex-direction: column;
                gap: 12px;
            `;

            riskData.forEach(risk => {
                const riskItem = document.createElement('div');
                riskItem.style.cssText = `
                    background: linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4));
                    border: 1px solid rgba(0, 255, 136, 0.2);
                    border-left: 4px solid ${getRiskColor(risk.riskLevel)};
                    border-radius: 8px;
                    padding: 15px;
                    transition: all 0.3s ease;
                    cursor: pointer;
                `;

                riskItem.addEventListener('mouseenter', () => {
                    riskItem.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.15), rgba(0, 0, 0, 0.5))';
                    riskItem.style.transform = 'translateX(5px)';
                });

                riskItem.addEventListener('mouseleave', () => {
                    riskItem.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4))';
                    riskItem.style.transform = 'translateX(0)';
                });

                riskItem.innerHTML = `
                    <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 10px;">
                        <div style="flex: 1;">
                            <div style="color: #ffffff; font-weight: 600; font-size: 0.95rem; margin-bottom: 4px;">${risk.milestone}</div>
                            <div style="color: #00ff88; font-size: 0.8rem; font-weight: 500;">${risk.project}</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="background: ${getRiskColor(risk.riskLevel)}20; color: ${getRiskColor(risk.riskLevel)}; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; margin-bottom: 4px;">
                                ${getRiskLevelText(risk.riskLevel)}
                            </div>
                            <div style="color: #ffffff; font-size: 0.8rem; font-family: 'Orbitron', monospace;">
                                风险值: <span style="color: ${getRiskColor(risk.riskLevel)}; font-weight: 700;">${risk.riskScore}</span>
                            </div>
                        </div>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <div style="color: #ffffff; font-size: 0.8rem; margin-bottom: 4px;">风险因素:</div>
                        <div style="display: flex; gap: 6px; flex-wrap: wrap;">
                            ${risk.factors.map(factor => `
                                <span style="background: rgba(255, 102, 0, 0.2); color: #ff6600; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                    ${factor}
                                </span>
                            `).join('')}
                        </div>
                    </div>
                    <div style="display: flex; justify-content: between; align-items: center;">
                        <div style="color: #ffffff; font-size: 0.8rem; opacity: 0.9;">
                            <i class="fas fa-clock" style="color: #00ff88; margin-right: 4px;"></i>
                            剩余 ${risk.daysLeft} 天
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span style="color: #ffffff; font-size: 0.8rem;">延期概率:</span>
                            <div style="width: 60px; height: 6px; background: rgba(0, 0, 0, 0.3); border-radius: 3px; overflow: hidden;">
                                <div style="width: ${risk.probability}%; height: 100%; background: ${getRiskColor(risk.riskLevel)}; border-radius: 3px;"></div>
                            </div>
                            <span style="color: ${getRiskColor(risk.riskLevel)}; font-weight: 700; font-size: 0.8rem; font-family: 'Orbitron', monospace;">
                                ${risk.probability}%
                            </span>
                        </div>
                    </div>
                `;

                riskListDiv.appendChild(riskItem);
            });

            container.appendChild(riskListDiv);
        }

        // 风险评估辅助函数
        function getRiskColor(level) {
            switch(level) {
                case 'high': return '#ff4444';
                case 'medium': return '#ff6600';
                case 'low': return '#00ff88';
                default: return '#8b949e';
            }
        }

        function getRiskLevelText(level) {
            switch(level) {
                case 'high': return '高风险';
                case 'medium': return '中风险';
                case 'low': return '低风险';
                default: return '未知';
            }
        }

        function refreshRiskAssessment() {
            initRiskAssessment();
            showNotification('风险评估已刷新', 'success');
        }

        // 里程碑相关辅助函数
        function getMilestoneColor(status) {
            switch(status) {
                case 'completed': return '#00ff88';
                case 'active': return '#ff6600';
                case 'pending': return '#8b949e';
                case 'delayed': return '#ff4444';
                default: return '#8b949e';
            }
        }

        function getMilestoneStatusBadge(status) {
            switch(status) {
                case 'completed': return 'bg-success';
                case 'active': return 'bg-warning';
                case 'pending': return 'bg-secondary';
                case 'delayed': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        function getMilestoneStatusText(status) {
            switch(status) {
                case 'completed': return '已完成';
                case 'active': return '进行中';
                case 'pending': return '待开始';
                case 'delayed': return '延期';
                default: return '未知';
            }
        }

        function showMilestoneDetails(milestone) {
            alert(`里程碑详情:\n名称: ${milestone.name}\n状态: ${getMilestoneStatusText(milestone.status)}\n进度: ${milestone.progress || 0}%`);
        }

        function addMilestone() {
            alert('新增里程碑功能 - 这里可以打开一个模态框来添加新的里程碑');
        }

        function refreshMilestones() {
            initMilestonesCharts();
        }

        function filterMilestones(status) {
            // 这里可以实现里程碑过滤功能
            console.log('过滤里程碑:', status);
            initMilestoneList(); // 重新加载列表
        }

        // 趋势预测功能
        function initTrendsCharts() {
            initTrendPredictionChart();
            initWarningAlerts();
            initCorrelationMatrix();
            initSeasonalAnalysis();
        }

        // 趋势预测图表
        function initTrendPredictionChart() {
            if (typeof Plotly === 'undefined') {
                document.getElementById('trend-prediction-chart').innerHTML = '<div style="color: #ff6600; text-align: center; padding: 20px;">图表库加载中...</div>';
                return;
            }

            // 历史数据
            const historicalData = {
                x: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'],
                y: [78, 82, 85, 88, 87, 90],
                type: 'scatter',
                mode: 'lines+markers',
                name: '历史数据',
                line: { color: '#00ff88', width: 3 },
                marker: { size: 8 }
            };

            // 预测数据
            const predictionData = {
                x: ['2024-06', '2024-07', '2024-08', '2024-09', '2024-10', '2024-11', '2024-12'],
                y: [90, 92, 94, 91, 93, 95, 97],
                type: 'scatter',
                mode: 'lines+markers',
                name: '预测数据',
                line: { color: '#ff6600', width: 3, dash: 'dash' },
                marker: { size: 8 }
            };

            // 置信区间
            const confidenceUpper = {
                x: ['2024-06', '2024-07', '2024-08', '2024-09', '2024-10', '2024-11', '2024-12'],
                y: [93, 96, 98, 95, 97, 99, 101],
                type: 'scatter',
                mode: 'lines',
                name: '置信区间上限',
                line: { color: 'rgba(255, 102, 0, 0.3)', width: 1 },
                fill: 'tonexty',
                fillcolor: 'rgba(255, 102, 0, 0.1)'
            };

            const confidenceLower = {
                x: ['2024-06', '2024-07', '2024-08', '2024-09', '2024-10', '2024-11', '2024-12'],
                y: [87, 88, 90, 87, 89, 91, 93],
                type: 'scatter',
                mode: 'lines',
                name: '置信区间下限',
                line: { color: 'rgba(255, 102, 0, 0.3)', width: 1 }
            };

            const data = [confidenceLower, confidenceUpper, historicalData, predictionData];

            const layout = {
                title: {
                    text: '项目达成率趋势预测分析',
                    font: {
                        color: '#ffffff',
                        size: 18,
                        family: 'Arial, sans-serif',
                        weight: 'bold'
                    },
                    x: 0.5,
                    xanchor: 'center'
                },
                xaxis: {
                    title: {
                        text: '时间',
                        font: {
                            color: '#ffffff',
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    tickfont: {
                        color: '#ffffff',
                        size: 12,
                        weight: 'bold'
                    },
                    gridcolor: 'rgba(255, 255, 255, 0.2)',
                    linecolor: '#ffffff',
                    zerolinecolor: 'rgba(255, 255, 255, 0.3)'
                },
                yaxis: {
                    title: {
                        text: '达成率 (%)',
                        font: {
                            color: '#ffffff',
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    tickfont: {
                        color: '#ffffff',
                        size: 12,
                        weight: 'bold'
                    },
                    gridcolor: 'rgba(255, 255, 255, 0.2)',
                    linecolor: '#ffffff',
                    zerolinecolor: 'rgba(255, 255, 255, 0.3)'
                },
                legend: {
                    font: {
                        color: '#ffffff',
                        size: 12,
                        weight: 'bold'
                    },
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                    bordercolor: 'rgba(0, 255, 136, 0.5)',
                    borderwidth: 1,
                    x: 0.02,
                    y: 0.98
                },
                paper_bgcolor: 'transparent',
                plot_bgcolor: 'rgba(0, 0, 0, 0.2)',
                font: {
                    color: '#ffffff',
                    size: 12,
                    weight: 'bold',
                    family: 'Arial, sans-serif'
                },
                margin: { t: 60, b: 60, l: 80, r: 40 }
            };

            Plotly.newPlot('trend-prediction-chart', data, layout, {responsive: true, displayModeBar: false});
        }



        // 预警信息
        function initWarningAlerts() {
            const alerts = [
                {
                    type: 'warning',
                    title: '项目C进度滞后',
                    message: '预计延期3天，建议增加资源投入',
                    time: '2分钟前'
                },
                {
                    type: 'info',
                    title: '季节性波动检测',
                    message: '检测到Q4季度达成率通常下降5%',
                    time: '15分钟前'
                },
                {
                    type: 'success',
                    title: '预测模型更新',
                    message: '模型准确率提升至94.2%',
                    time: '1小时前'
                },
                {
                    type: 'danger',
                    title: '高风险项目预警',
                    message: '项目D存在严重延期风险',
                    time: '2小时前'
                }
            ];

            const container = document.getElementById('warning-alerts');
            container.innerHTML = '';

            alerts.forEach(alert => {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert-item mb-2 p-2`;
                alertDiv.style.cssText = `
                    background: linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4));
                    border: 1px solid rgba(0, 255, 136, 0.2);
                    border-radius: 8px;
                    border-left: 4px solid ${getAlertColor(alert.type)};
                    transition: all 0.3s ease;
                    cursor: pointer;
                `;

                alertDiv.addEventListener('mouseenter', () => {
                    alertDiv.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.15), rgba(0, 0, 0, 0.5))';
                    alertDiv.style.transform = 'translateX(5px)';
                });

                alertDiv.addEventListener('mouseleave', () => {
                    alertDiv.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4))';
                    alertDiv.style.transform = 'translateX(0)';
                });

                alertDiv.innerHTML = `
                    <div class="d-flex align-items-start">
                        <i class="fas ${getAlertIcon(alert.type)} me-2" style="color: ${getAlertColor(alert.type)}; margin-top: 2px; font-size: 1.1rem; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);"></i>
                        <div class="flex-grow-1">
                            <div style="font-size: 0.95rem; font-weight: 600; color: #ffffff; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8); margin-bottom: 4px;">${alert.title}</div>
                            <div style="font-size: 0.85rem; color: #ffffff; opacity: 0.9; font-weight: 500; margin-bottom: 4px;">${alert.message}</div>
                            <small style="color: #00ff88; font-weight: 500; font-size: 0.75rem;">
                                <i class="fas fa-clock" style="margin-right: 4px;"></i>${alert.time}
                            </small>
                        </div>
                    </div>
                `;

                container.appendChild(alertDiv);
            });
        }

        // 关联分析矩阵
        function initCorrelationMatrix() {
            if (typeof Plotly === 'undefined') {
                document.getElementById('correlation-matrix').innerHTML = '<div style="color: #ff6600; text-align: center; padding: 20px;">图表库加载中...</div>';
                return;
            }

            const variables = ['需求变更', '团队规模', '技术复杂度', '时间压力', '资源投入'];
            const correlationData = [
                [1.0, 0.3, 0.7, 0.5, -0.2],
                [0.3, 1.0, 0.4, 0.2, 0.8],
                [0.7, 0.4, 1.0, 0.6, 0.1],
                [0.5, 0.2, 0.6, 1.0, -0.3],
                [-0.2, 0.8, 0.1, -0.3, 1.0]
            ];

            const data = [{
                z: correlationData,
                x: variables,
                y: variables,
                type: 'heatmap',
                colorscale: [
                    [0, '#ff6600'],
                    [0.5, '#ffffff'],
                    [1, '#00ff88']
                ],
                showscale: true,
                colorbar: {
                    title: {
                        text: '相关系数',
                        font: {
                            color: '#ffffff',
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    tickfont: {
                        color: '#ffffff',
                        size: 11,
                        weight: 'bold'
                    },
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                    bordercolor: 'rgba(0, 255, 136, 0.5)',
                    borderwidth: 1
                }
            }];

            const layout = {
                title: {
                    text: '因素关联度分析',
                    font: {
                        color: '#ffffff',
                        size: 16,
                        weight: 'bold',
                        family: 'Arial, sans-serif'
                    },
                    x: 0.5,
                    xanchor: 'center'
                },
                xaxis: {
                    tickfont: {
                        color: '#ffffff',
                        size: 11,
                        weight: 'bold'
                    },
                    gridcolor: 'rgba(255, 255, 255, 0.2)',
                    linecolor: '#ffffff'
                },
                yaxis: {
                    tickfont: {
                        color: '#ffffff',
                        size: 11,
                        weight: 'bold'
                    },
                    gridcolor: 'rgba(255, 255, 255, 0.2)',
                    linecolor: '#ffffff'
                },
                paper_bgcolor: 'transparent',
                plot_bgcolor: 'rgba(0, 0, 0, 0.2)',
                font: {
                    color: '#ffffff',
                    size: 12,
                    weight: 'bold',
                    family: 'Arial, sans-serif'
                },
                margin: { t: 50, b: 50, l: 80, r: 40 }
            };

            Plotly.newPlot('correlation-matrix', data, layout, {responsive: true, displayModeBar: false});
        }

        // 季节性分析
        function initSeasonalAnalysis() {
            if (typeof Plotly === 'undefined') {
                document.getElementById('seasonal-analysis').innerHTML = '<div style="color: #ff6600; text-align: center; padding: 20px;">图表库加载中...</div>';
                return;
            }

            const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            const seasonalPattern = [85, 87, 90, 92, 94, 96, 95, 93, 91, 88, 86, 84];
            const currentYear = [78, 82, 85, 88, 87, 90, 0, 0, 0, 0, 0, 0];

            const historicalTrace = {
                x: months,
                y: seasonalPattern,
                type: 'scatter',
                mode: 'lines+markers',
                name: '历史季节性模式',
                line: { color: '#ffffff', width: 2, dash: 'dot' },
                marker: { size: 6, color: '#ffffff', opacity: 0.8 }
            };

            const currentTrace = {
                x: months.slice(0, 6),
                y: currentYear.slice(0, 6),
                type: 'scatter',
                mode: 'lines+markers',
                name: '当前年度',
                line: { color: '#00ff88', width: 3 },
                marker: { size: 8 }
            };

            const data = [historicalTrace, currentTrace];

            const layout = {
                title: {
                    text: '季节性达成率模式',
                    font: {
                        color: '#ffffff',
                        size: 16,
                        weight: 'bold',
                        family: 'Arial, sans-serif'
                    },
                    x: 0.5,
                    xanchor: 'center'
                },
                xaxis: {
                    title: {
                        text: '月份',
                        font: {
                            color: '#ffffff',
                            size: 13,
                            weight: 'bold'
                        }
                    },
                    tickfont: {
                        color: '#ffffff',
                        size: 11,
                        weight: 'bold'
                    },
                    gridcolor: 'rgba(255, 255, 255, 0.2)',
                    linecolor: '#ffffff',
                    zerolinecolor: 'rgba(255, 255, 255, 0.3)'
                },
                yaxis: {
                    title: {
                        text: '达成率 (%)',
                        font: {
                            color: '#ffffff',
                            size: 13,
                            weight: 'bold'
                        }
                    },
                    tickfont: {
                        color: '#ffffff',
                        size: 11,
                        weight: 'bold'
                    },
                    gridcolor: 'rgba(255, 255, 255, 0.2)',
                    linecolor: '#ffffff',
                    zerolinecolor: 'rgba(255, 255, 255, 0.3)'
                },
                legend: {
                    font: {
                        color: '#ffffff',
                        size: 12,
                        weight: 'bold'
                    },
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                    bordercolor: 'rgba(0, 255, 136, 0.5)',
                    borderwidth: 1,
                    x: 0.02,
                    y: 0.98
                },
                paper_bgcolor: 'transparent',
                plot_bgcolor: 'rgba(0, 0, 0, 0.2)',
                font: {
                    color: '#ffffff',
                    size: 12,
                    weight: 'bold',
                    family: 'Arial, sans-serif'
                },
                margin: { t: 50, b: 50, l: 60, r: 40 }
            };

            Plotly.newPlot('seasonal-analysis', data, layout, {responsive: true, displayModeBar: false});
        }

        // 趋势预测辅助函数
        function getAlertColor(type) {
            switch(type) {
                case 'success': return '#00ff88';
                case 'warning': return '#ffff00';
                case 'danger': return '#ff6600';
                case 'info': return '#0066cc';
                default: return '#8b949e';
            }
        }

        function getAlertIcon(type) {
            switch(type) {
                case 'success': return 'fa-check-circle';
                case 'warning': return 'fa-exclamation-triangle';
                case 'danger': return 'fa-times-circle';
                case 'info': return 'fa-info-circle';
                default: return 'fa-bell';
            }
        }

        function changePredictionModel(model) {
            console.log('切换预测模型:', model);
            // 这里可以实现模型切换逻辑
        }

        function runPrediction() {
            // 模拟运行预测
            alert('正在运行AI预测分析...\n预计完成时间: 30秒');
            setTimeout(() => {
                alert('预测分析完成!\n准确率: 94.2%\n置信区间: ±3.5%');
            }, 2000);
        }

        function exportPrediction() {
            alert('导出预测报告功能 - 这里可以生成PDF或Excel报告');
        }

        function togglePredictionView() {
            // 切换预测视图
            initTrendPredictionChart();
        }

        function refreshTrendChart() {
            initTrendPredictionChart();
        }

        // 深度分析功能
        function initAnalyticsCharts() {
            initNetworkGraph();
            initPerformanceRadar();
            initAnomalyDetection();
            initAISuggestions();
        }

        // 高级力导向网络图
        function initNetworkGraph() {
            if (typeof d3 === 'undefined') {
                document.getElementById('network-graph').innerHTML = '<div style="color: #ff6600; text-align: center; padding: 20px;">D3.js库加载中...</div>';
                return;
            }

            const container = document.getElementById('network-graph');
            container.innerHTML = '';

            const width = container.clientWidth;
            const height = 350;

            const svg = d3.select('#network-graph')
                .append('svg')
                .attr('width', '100%')
                .attr('height', '100%')
                .attr('viewBox', `0 0 ${width} ${height}`);

            // 添加渐变定义
            const defs = svg.append('defs');

            // 创建发光效果滤镜
            const filter = defs.append('filter')
                .attr('id', 'glow');

            filter.append('feGaussianBlur')
                .attr('stdDeviation', '3')
                .attr('result', 'coloredBlur');

            const feMerge = filter.append('feMerge');
            feMerge.append('feMergeNode').attr('in', 'coloredBlur');
            feMerge.append('feMergeNode').attr('in', 'SourceGraphic');

            // 更复杂的节点数据
            const nodes = [
                {id: 'ERP核心', group: 1, size: 25, importance: 10, type: 'core'},
                {id: '移动应用', group: 2, size: 20, importance: 8, type: 'frontend'},
                {id: '数据中心', group: 2, size: 22, importance: 9, type: 'backend'},
                {id: '安全网关', group: 3, size: 18, importance: 7, type: 'security'},
                {id: '客户服务', group: 3, size: 16, importance: 6, type: 'service'},
                {id: '供应链', group: 4, size: 19, importance: 7, type: 'business'},
                {id: 'AI引擎', group: 4, size: 21, importance: 8, type: 'ai'},
                {id: '报表系统', group: 5, size: 15, importance: 5, type: 'report'},
                {id: '监控中心', group: 5, size: 17, importance: 6, type: 'monitor'}
            ];

            const links = [
                {source: 'ERP核心', target: '移动应用', strength: 0.8, type: 'api'},
                {source: 'ERP核心', target: '数据中心', strength: 0.9, type: 'data'},
                {source: 'ERP核心', target: '安全网关', strength: 0.7, type: 'security'},
                {source: '数据中心', target: '客户服务', strength: 0.6, type: 'service'},
                {source: '数据中心', target: 'AI引擎', strength: 0.8, type: 'ml'},
                {source: '安全网关', target: '供应链', strength: 0.5, type: 'business'},
                {source: 'AI引擎', target: '报表系统', strength: 0.7, type: 'analytics'},
                {source: '监控中心', target: 'ERP核心', strength: 0.6, type: 'monitor'},
                {source: '监控中心', target: '数据中心', strength: 0.5, type: 'monitor'}
            ];

            // 创建力导向模拟
            const simulation = d3.forceSimulation(nodes)
                .force('link', d3.forceLink(links).id(d => d.id).distance(80).strength(d => d.strength))
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(width / 2, height / 2))
                .force('collision', d3.forceCollide().radius(d => d.size + 5));

            // 绘制连接线
            const link = svg.append('g')
                .selectAll('line')
                .data(links)
                .enter()
                .append('line')
                .attr('stroke', d => {
                    const colors = {
                        'api': '#00ff88',
                        'data': '#0066cc',
                        'security': '#ff6600',
                        'service': '#ffff00',
                        'ml': '#ff00ff',
                        'business': '#00ffff',
                        'analytics': '#ff8800',
                        'monitor': '#8800ff'
                    };
                    return colors[d.type] || '#ffffff';
                })
                .attr('stroke-width', d => Math.sqrt(d.strength * 5))
                .attr('stroke-opacity', 0.6)
                .style('filter', 'url(#glow)');

            // 绘制节点
            const node = svg.append('g')
                .selectAll('g')
                .data(nodes)
                .enter()
                .append('g')
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));

            // 节点圆圈
            node.append('circle')
                .attr('r', d => d.size)
                .attr('fill', d => {
                    const colors = {
                        'core': '#00ff88',
                        'frontend': '#0066cc',
                        'backend': '#ff6600',
                        'security': '#ff0066',
                        'service': '#ffff00',
                        'business': '#00ffff',
                        'ai': '#ff00ff',
                        'report': '#ff8800',
                        'monitor': '#8800ff'
                    };
                    return colors[d.type] || '#ffffff';
                })
                .attr('stroke', '#ffffff')
                .attr('stroke-width', 2)
                .style('filter', 'url(#glow)')
                .on('mouseover', function(event, d) {
                    d3.select(this).transition().duration(200).attr('r', d.size * 1.2);
                })
                .on('mouseout', function(event, d) {
                    d3.select(this).transition().duration(200).attr('r', d.size);
                });

            // 节点标签
            node.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', d => d.size + 15)
                .attr('fill', '#ffffff')
                .attr('font-size', '10px')
                .attr('font-weight', 'bold')
                .style('text-shadow', '1px 1px 2px rgba(0, 0, 0, 0.8)')
                .text(d => d.id);

            // 重要性指示器
            node.append('circle')
                .attr('r', 3)
                .attr('cx', d => d.size - 5)
                .attr('cy', d => -d.size + 5)
                .attr('fill', '#ffff00')
                .attr('opacity', d => d.importance / 10);

            // 更新位置
            simulation.on('tick', () => {
                link
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);

                node
                    .attr('transform', d => `translate(${d.x}, ${d.y})`);
            });

            // 拖拽函数
            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }

            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }

            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }
        }

        // 高级多维性能分析图
        function initPerformanceRadar() {
            if (typeof Plotly === 'undefined') {
                document.getElementById('performance-radar').innerHTML = '<div style="color: #ff6600; text-align: center; padding: 20px;">图表库加载中...</div>';
                return;
            }

            // 多层次性能数据
            const currentPerformance = [85, 92, 78, 88, 90, 87, 83, 91, 86, 89];
            const targetPerformance = [90, 95, 85, 92, 88, 92, 85, 94, 90, 93];
            const industryAverage = [75, 80, 70, 78, 82, 79, 75, 83, 77, 81];
            const bestPractice = [95, 98, 92, 96, 94, 96, 90, 97, 94, 96];

            const dimensions = [
                '开发效率', '代码质量', '成本控制', '交付时间',
                '风险管理', '客户满意', '技术创新', '团队协作',
                '资源利用', '流程优化'
            ];

            const data = [
                {
                    type: 'scatterpolar',
                    r: currentPerformance,
                    theta: dimensions,
                    fill: 'toself',
                    name: '当前性能',
                    line: {
                        color: '#00ff88',
                        width: 3,
                        dash: 'solid'
                    },
                    fillcolor: 'rgba(0, 255, 136, 0.2)',
                    marker: {
                        size: 8,
                        color: '#00ff88',
                        symbol: 'circle'
                    }
                },
                {
                    type: 'scatterpolar',
                    r: targetPerformance,
                    theta: dimensions,
                    fill: 'toself',
                    name: '目标性能',
                    line: {
                        color: '#ff6600',
                        width: 3,
                        dash: 'dash'
                    },
                    fillcolor: 'rgba(255, 102, 0, 0.15)',
                    marker: {
                        size: 8,
                        color: '#ff6600',
                        symbol: 'diamond'
                    }
                },
                {
                    type: 'scatterpolar',
                    r: industryAverage,
                    theta: dimensions,
                    fill: 'none',
                    name: '行业平均',
                    line: {
                        color: '#0066cc',
                        width: 2,
                        dash: 'dot'
                    },
                    marker: {
                        size: 6,
                        color: '#0066cc',
                        symbol: 'square'
                    }
                },
                {
                    type: 'scatterpolar',
                    r: bestPractice,
                    theta: dimensions,
                    fill: 'none',
                    name: '最佳实践',
                    line: {
                        color: '#ffff00',
                        width: 2,
                        dash: 'dashdot'
                    },
                    marker: {
                        size: 6,
                        color: '#ffff00',
                        symbol: 'star'
                    }
                }
            ];

            const layout = {
                title: {
                    text: '多维度性能对比分析',
                    font: {
                        color: '#ffffff',
                        size: 16,
                        weight: 'bold',
                        family: 'Arial, sans-serif'
                    },
                    x: 0.5,
                    xanchor: 'center'
                },
                polar: {
                    bgcolor: 'rgba(0, 0, 0, 0.1)',
                    radialaxis: {
                        visible: true,
                        range: [0, 100],
                        color: '#ffffff',
                        tickfont: {
                            color: '#ffffff',
                            size: 10,
                            weight: 'bold'
                        },
                        gridcolor: 'rgba(255, 255, 255, 0.3)',
                        linecolor: 'rgba(255, 255, 255, 0.5)',
                        tickmode: 'linear',
                        tick0: 0,
                        dtick: 20,
                        showticklabels: true
                    },
                    angularaxis: {
                        color: '#ffffff',
                        tickfont: {
                            color: '#ffffff',
                            size: 11,
                            weight: 'bold'
                        },
                        gridcolor: 'rgba(255, 255, 255, 0.4)',
                        linecolor: 'rgba(255, 255, 255, 0.6)',
                        rotation: 90,
                        direction: 'clockwise'
                    }
                },
                showlegend: true,
                legend: {
                    font: {
                        color: '#ffffff',
                        size: 11,
                        weight: 'bold'
                    },
                    bgcolor: 'rgba(0, 0, 0, 0.8)',
                    bordercolor: 'rgba(0, 255, 136, 0.5)',
                    borderwidth: 1,
                    x: 1.02,
                    y: 1,
                    xanchor: 'left',
                    yanchor: 'top'
                },
                paper_bgcolor: 'transparent',
                plot_bgcolor: 'transparent',
                font: {
                    color: '#ffffff',
                    size: 11,
                    weight: 'bold',
                    family: 'Arial, sans-serif'
                },
                margin: { t: 60, b: 40, l: 40, r: 120 },
                annotations: [
                    {
                        text: '性能指标范围: 0-100',
                        showarrow: false,
                        x: 0.5,
                        y: -0.1,
                        xref: 'paper',
                        yref: 'paper',
                        font: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            size: 10
                        }
                    }
                ]
            };

            const config = {
                responsive: true,
                displayModeBar: false,
                staticPlot: false
            };

            Plotly.newPlot('performance-radar', data, layout, config);
        }

        // 异常检测
        function initAnomalyDetection() {
            if (typeof Plotly === 'undefined') {
                document.getElementById('anomaly-detection').innerHTML = '<div style="color: #ff6600; text-align: center; padding: 20px;">图表库加载中...</div>';
                return;
            }

            const normalData = [];
            const anomalyData = [];

            for (let i = 0; i < 100; i++) {
                const x = i;
                const y = 80 + Math.sin(i * 0.1) * 10 + Math.random() * 5;

                if (i === 25 || i === 67 || i === 89) {
                    anomalyData.push({x: x, y: y + 20});
                } else {
                    normalData.push({x: x, y: y});
                }
            }

            const normalTrace = {
                x: normalData.map(d => d.x),
                y: normalData.map(d => d.y),
                type: 'scatter',
                mode: 'markers',
                name: '正常数据',
                marker: { color: '#00ff88', size: 4 }
            };

            const anomalyTrace = {
                x: anomalyData.map(d => d.x),
                y: anomalyData.map(d => d.y),
                type: 'scatter',
                mode: 'markers',
                name: '异常数据',
                marker: { color: '#ff6600', size: 8, symbol: 'x' }
            };

            const data = [normalTrace, anomalyTrace];

            const layout = {
                title: {
                    text: '项目性能异常检测',
                    font: {
                        color: '#ffffff',
                        size: 16,
                        weight: 'bold',
                        family: 'Arial, sans-serif'
                    },
                    x: 0.5,
                    xanchor: 'center'
                },
                xaxis: {
                    title: {
                        text: '时间序列',
                        font: {
                            color: '#ffffff',
                            size: 13,
                            weight: 'bold'
                        }
                    },
                    tickfont: {
                        color: '#ffffff',
                        size: 11,
                        weight: 'bold'
                    },
                    gridcolor: 'rgba(255, 255, 255, 0.2)',
                    linecolor: '#ffffff',
                    zerolinecolor: 'rgba(255, 255, 255, 0.3)'
                },
                yaxis: {
                    title: {
                        text: '性能指标',
                        font: {
                            color: '#ffffff',
                            size: 13,
                            weight: 'bold'
                        }
                    },
                    tickfont: {
                        color: '#ffffff',
                        size: 11,
                        weight: 'bold'
                    },
                    gridcolor: 'rgba(255, 255, 255, 0.2)',
                    linecolor: '#ffffff',
                    zerolinecolor: 'rgba(255, 255, 255, 0.3)'
                },
                legend: {
                    font: {
                        color: '#ffffff',
                        size: 12,
                        weight: 'bold'
                    },
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                    bordercolor: 'rgba(0, 255, 136, 0.5)',
                    borderwidth: 1,
                    x: 0.02,
                    y: 0.98
                },
                paper_bgcolor: 'transparent',
                plot_bgcolor: 'rgba(0, 0, 0, 0.2)',
                font: {
                    color: '#ffffff',
                    size: 12,
                    weight: 'bold',
                    family: 'Arial, sans-serif'
                },
                margin: { t: 50, b: 50, l: 60, r: 40 }
            };

            Plotly.newPlot('anomaly-detection', data, layout, {responsive: true, displayModeBar: false});
        }

        // AI智能建议
        function initAISuggestions() {
            const suggestions = [
                {
                    type: 'optimization',
                    title: '资源优化建议',
                    content: '建议将项目A的2名开发人员临时调配至项目C，可提升整体效率15%',
                    confidence: 92,
                    impact: 'high'
                },
                {
                    type: 'risk',
                    title: '风险预警',
                    content: '项目D的当前进度存在延期风险，建议提前2周开始测试阶段',
                    confidence: 87,
                    impact: 'medium'
                },
                {
                    type: 'process',
                    title: '流程改进',
                    content: '代码审查环节可采用自动化工具，预计节省30%的时间成本',
                    confidence: 78,
                    impact: 'medium'
                },
                {
                    type: 'quality',
                    title: '质量提升',
                    content: '增加单元测试覆盖率至85%，可降低生产环境bug率40%',
                    confidence: 95,
                    impact: 'high'
                }
            ];

            const container = document.getElementById('ai-suggestions');
            container.innerHTML = '';

            suggestions.forEach(suggestion => {
                const suggestionDiv = document.createElement('div');
                suggestionDiv.className = 'suggestion-item mb-3 p-3';
                suggestionDiv.style.cssText = `
                    background: linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4));
                    border: 1px solid rgba(0, 255, 136, 0.2);
                    border-radius: 8px;
                    border-left: 4px solid ${getSuggestionColor(suggestion.type)};
                    transition: all 0.3s ease;
                    cursor: pointer;
                `;

                suggestionDiv.addEventListener('mouseenter', () => {
                    suggestionDiv.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.15), rgba(0, 0, 0, 0.5))';
                    suggestionDiv.style.transform = 'translateY(-2px)';
                });

                suggestionDiv.addEventListener('mouseleave', () => {
                    suggestionDiv.style.background = 'linear-gradient(135deg, rgba(0, 255, 136, 0.08), rgba(0, 0, 0, 0.4))';
                    suggestionDiv.style.transform = 'translateY(0)';
                });

                suggestionDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div style="font-size: 0.95rem; font-weight: 600; color: #ffffff; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);">${suggestion.title}</div>
                        <span class="badge ${getImpactBadge(suggestion.impact)}" style="font-weight: 600;">${getImpactText(suggestion.impact)}</span>
                    </div>
                    <div class="suggestion-content mb-2" style="font-size: 0.85rem; color: #ffffff; opacity: 0.9; font-weight: 500; line-height: 1.4;">
                        ${suggestion.content}
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small style="color: #00ff88; font-weight: 500; font-size: 0.75rem;">
                            <i class="fas fa-brain" style="margin-right: 4px;"></i>AI置信度: <span style="font-weight: 700;">${suggestion.confidence}%</span>
                        </small>
                        <button class="btn btn-sm btn-outline-primary" onclick="applySuggestion('${suggestion.type}')" style="font-weight: 600;">
                            <i class="fas fa-check"></i> 采纳
                        </button>
                    </div>
                `;

                container.appendChild(suggestionDiv);
            });
        }

        // 3D场景功能

        function init3DScene() {
            try {
                const container = document.getElementById('three-scene');
                if (!container) return;

                container.innerHTML = '';

                // 检查Three.js是否加载
                if (typeof THREE === 'undefined' || !THREE.Scene || !THREE.Color) {
                    console.log('THREE.js未加载，使用HTML替代方案');
                    create3DFallback(container);
                    return;
                }

                // 创建场景
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x0d1117);

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.set(0, 5, 10);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            container.appendChild(renderer.domElement);

            // 创建控制器
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;

            // 添加光源
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0x00ff88, 1);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);

            // 创建项目里程碑3D模型
            create3DProjectStructure();

            // 开始渲染循环
            animate3D();

            // 监听窗口大小变化
            window.addEventListener('resize', onWindowResize);

            } catch (error) {
                console.error('3D场景初始化失败:', error);
                const container = document.getElementById('three-scene');
                if (container) {
                    container.innerHTML = '<div style="color: #ff6600; text-align: center; padding: 50px;">3D场景初始化失败，请检查浏览器兼容性</div>';
                }
            }
        }

        function create3DProjectStructure() {
            // 创建中心主体 - 代表主项目
            const mainGeometry = new THREE.BoxGeometry(2, 2, 2);
            const mainMaterial = new THREE.MeshPhongMaterial({
                color: 0x00ff88,
                transparent: true,
                opacity: 0.8
            });
            const mainCube = new THREE.Mesh(mainGeometry, mainMaterial);
            mainCube.position.set(0, 0, 0);
            mainCube.castShadow = true;
            scene.add(mainCube);

            // 创建里程碑节点
            const milestones = [
                { pos: [-4, 2, 0], color: 0x00ff88, status: 'completed' },
                { pos: [4, 2, 0], color: 0xff6600, status: 'active' },
                { pos: [0, 4, -4], color: 0x8b949e, status: 'pending' },
                { pos: [0, 4, 4], color: 0x8b949e, status: 'pending' },
                { pos: [-3, -2, 3], color: 0x00ff88, status: 'completed' },
                { pos: [3, -2, -3], color: 0xff6600, status: 'active' }
            ];

            milestones.forEach((milestone, index) => {
                const geometry = new THREE.SphereGeometry(0.5, 16, 16);
                const material = new THREE.MeshPhongMaterial({
                    color: milestone.color,
                    transparent: true,
                    opacity: 0.9
                });
                const sphere = new THREE.Mesh(geometry, material);
                sphere.position.set(...milestone.pos);
                sphere.castShadow = true;
                sphere.userData = { type: 'milestone', index: index, status: milestone.status };
                scene.add(sphere);

                // 添加连接线
                const lineGeometry = new THREE.BufferGeometry().setFromPoints([
                    new THREE.Vector3(0, 0, 0),
                    new THREE.Vector3(...milestone.pos)
                ]);
                const lineMaterial = new THREE.LineBasicMaterial({
                    color: milestone.color,
                    transparent: true,
                    opacity: 0.6
                });
                const line = new THREE.Line(lineGeometry, lineMaterial);
                scene.add(line);
            });

            // 创建数据流动效果
            createDataFlowEffect();

            // 添加文字标签
            addTextLabels();
        }

        function createDataFlowEffect() {
            const particleCount = 50;
            const particles = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);

            for (let i = 0; i < particleCount; i++) {
                positions[i * 3] = (Math.random() - 0.5) * 20;
                positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
                positions[i * 3 + 2] = (Math.random() - 0.5) * 20;
            }

            particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));

            const particleMaterial = new THREE.PointsMaterial({
                color: 0x00ff88,
                size: 0.1,
                transparent: true,
                opacity: 0.8
            });

            const particleSystem = new THREE.Points(particles, particleMaterial);
            scene.add(particleSystem);
        }

        function addTextLabels() {
            // 这里可以添加3D文字标签
            // 由于Three.js文字渲染较复杂，这里简化处理
        }

        function animate3D() {
            animationId = requestAnimationFrame(animate3D);

            // 自动旋转
            if (autoRotateEnabled) {
                scene.rotation.y += 0.005;
            }

            // 更新控制器
            controls.update();

            // 渲染场景
            renderer.render(scene, camera);

            // 更新信息面板
            updateSceneInfo();
        }

        function updateSceneInfo() {
            // 更新FPS
            const fps = Math.round(1000 / 16.67); // 简化的FPS计算
            document.getElementById('fps-counter').textContent = `${fps} FPS`;

            // 更新相机位置
            const pos = camera.position;
            document.getElementById('camera-position').textContent =
                `X:${pos.x.toFixed(1)} Y:${pos.y.toFixed(1)} Z:${pos.z.toFixed(1)}`;
        }

        function onWindowResize() {
            const container = document.getElementById('three-scene');
            if (container && camera && renderer) {
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        }

        // 数据挖掘控制台函数
        function runDataMining() {
            const progressDiv = document.getElementById('mining-progress');
            const progressBar = progressDiv.querySelector('.progress-bar');
            const progressText = progressDiv.querySelector('small');

            progressDiv.style.display = 'block';
            progressBar.style.width = '0%';

            const steps = [
                { progress: 20, text: '正在加载数据集...' },
                { progress: 40, text: '执行特征提取...' },
                { progress: 60, text: '应用机器学习算法...' },
                { progress: 80, text: '生成分析结果...' },
                { progress: 100, text: '数据挖掘完成！' }
            ];

            let currentStep = 0;

            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    progressBar.style.width = step.progress + '%';
                    progressText.textContent = step.text;
                    currentStep++;
                } else {
                    clearInterval(interval);
                    setTimeout(() => {
                        progressDiv.style.display = 'none';
                        showMiningResults();
                    }, 1000);
                }
            }, 800);
        }

        function generateInsights() {
            const insights = [
                '发现项目A与项目C存在资源竞争关系',
                '识别出开发效率的3个关键瓶颈',
                '预测下季度需求增长23%',
                '建议优化代码审查流程可节省15%时间'
            ];

            const randomInsight = insights[Math.floor(Math.random() * insights.length)];

            // 创建临时提示
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, rgba(0, 255, 136, 0.9), rgba(0, 102, 204, 0.9));
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                font-weight: 600;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 255, 136, 0.4);
                max-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;
            toast.innerHTML = `
                <div style="display: flex; align-items: center;">
                    <i class="fas fa-lightbulb" style="margin-right: 10px; font-size: 1.2rem;"></i>
                    <div>
                        <div style="font-size: 0.9rem; margin-bottom: 5px;">AI洞察</div>
                        <div style="font-size: 0.8rem; opacity: 0.9;">${randomInsight}</div>
                    </div>
                </div>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 4000);
        }

        function changeMiningAlgorithm(algorithm) {
            const algorithms = {
                'clustering': '聚类分析 - 识别数据模式和分组',
                'association': '关联规则 - 发现项目间关联性',
                'classification': '分类预测 - 预测项目成功率',
                'regression': '回归分析 - 预测性能趋势',
                'anomaly': '异常检测 - 识别异常行为模式'
            };

            console.log(`切换到算法: ${algorithms[algorithm]}`);
            updateMiningMetrics(algorithm);
        }

        function changeDataRange(range) {
            const ranges = {
                'last7days': '最近7天数据',
                'last30days': '最近30天数据',
                'last90days': '最近90天数据',
                'lastyear': '最近一年数据',
                'all': '全部历史数据'
            };

            console.log(`数据范围: ${ranges[range]}`);
            updateMiningMetrics(null, range);
        }

        function updateMiningMetrics(algorithm, range) {
            // 模拟更新指标
            const metrics = document.querySelectorAll('.mining-metric-value');
            metrics.forEach(metric => {
                const currentValue = metric.textContent;
                metric.style.opacity = '0.5';

                setTimeout(() => {
                    // 随机更新数值
                    if (currentValue.includes('TB')) {
                        metric.textContent = (Math.random() * 5 + 1).toFixed(1) + 'TB';
                    } else if (currentValue.includes('%')) {
                        metric.textContent = (Math.random() * 10 + 90).toFixed(1) + '%';
                    } else if (currentValue.includes('s')) {
                        metric.textContent = (Math.random() * 5 + 1).toFixed(1) + 's';
                    } else {
                        metric.textContent = Math.floor(Math.random() * 1000 + 500);
                    }
                    metric.style.opacity = '1';
                }, 300);
            });
        }

        function showMiningResults() {
            // 这里可以显示挖掘结果
            console.log('显示数据挖掘结果');
        }

        // 深度分析辅助函数
        function getSuggestionColor(type) {
            switch(type) {
                case 'optimization': return '#00ff88';
                case 'risk': return '#ff6600';
                case 'process': return '#0066cc';
                case 'quality': return '#ffff00';
                default: return '#8b949e';
            }
        }

        function getImpactBadge(impact) {
            switch(impact) {
                case 'high': return 'bg-danger';
                case 'medium': return 'bg-warning';
                case 'low': return 'bg-success';
                default: return 'bg-secondary';
            }
        }

        function getImpactText(impact) {
            switch(impact) {
                case 'high': return '高影响';
                case 'medium': return '中影响';
                case 'low': return '低影响';
                default: return '未知';
            }
        }

        function runDataMining() {
            const progressBar = document.querySelector('#mining-progress .progress-bar');
            const progressContainer = document.getElementById('mining-progress');

            progressContainer.style.display = 'block';
            let progress = 0;

            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    setTimeout(() => {
                        progressContainer.style.display = 'none';
                        alert('数据挖掘完成！发现3个关键模式和5个优化建议。');
                    }, 500);
                }
                progressBar.style.width = progress + '%';
            }, 200);
        }

        function generateInsights() {
            alert('AI洞察生成中...\n\n发现的关键洞察:\n1. 项目延期主要由需求变更引起\n2. 团队协作效率在周三最高\n3. 代码质量与测试覆盖率强相关');
        }

        function applySuggestion(type) {
            alert(`正在应用${type}类型的建议...`);
        }

        // 3D控制函数
        function reset3DView() {
            if (camera && controls) {
                camera.position.set(0, 5, 10);
                controls.reset();
            }
        }

        function animate3DScene() {
            if (scene) {
                // 添加旋转动画
                const objects = scene.children.filter(child => child.userData.type === 'milestone');
                objects.forEach((obj, index) => {
                    const originalY = obj.position.y;
                    const animate = () => {
                        obj.position.y = originalY + Math.sin(Date.now() * 0.001 + index) * 0.5;
                        obj.rotation.y += 0.02;
                    };
                    animate();
                });
            }
        }

        function toggle3DMode() {
            autoRotateEnabled = !autoRotateEnabled;
            const button = event.target;
            button.innerHTML = autoRotateEnabled ?
                '<i class="fas fa-pause"></i> 停止旋转' :
                '<i class="fas fa-play"></i> 自动旋转';
        }

        function setView(viewType) {
            if (!camera) return;

            switch(viewType) {
                case 'top':
                    camera.position.set(0, 15, 0);
                    camera.lookAt(0, 0, 0);
                    break;
                case 'side':
                    camera.position.set(15, 0, 0);
                    camera.lookAt(0, 0, 0);
                    break;
                case 'perspective':
                    camera.position.set(10, 10, 10);
                    camera.lookAt(0, 0, 0);
                    break;
            }
        }

        function change3DMode(mode) {
            console.log('切换3D模式:', mode);
            // 这里可以实现不同的3D显示模式
        }

        function setRenderQuality(quality) {
            if (renderer) {
                const pixelRatio = quality === '1' ? 0.5 : quality === '2' ? 1 : 2;
                renderer.setPixelRatio(pixelRatio);
            }
        }

        function toggleAutoRotate(enabled) {
            autoRotateEnabled = enabled;
        }

        // 数据模拟和动画效果
        function startDataSimulation() {
            // 模拟实时数据更新
            setInterval(() => {
                updateMetrics();
            }, 5000);

            // 添加页面加载动画
            addLoadingAnimations();
        }

        function updateMetrics() {
            // 随机更新关键指标
            const activeProjects = document.getElementById('active-projects');
            const completionRate = document.getElementById('completion-rate');
            const completedMilestones = document.getElementById('completed-milestones');
            const riskProjects = document.getElementById('risk-projects');

            if (activeProjects) {
                const currentValue = parseInt(activeProjects.textContent);
                const newValue = Math.max(8, Math.min(20, currentValue + (Math.random() - 0.5) * 2));
                animateNumber(activeProjects, Math.round(newValue));
            }

            if (completionRate) {
                const currentValue = parseFloat(completionRate.textContent);
                const newValue = Math.max(70, Math.min(100, currentValue + (Math.random() - 0.5) * 5));
                animateNumber(completionRate, newValue.toFixed(1));
            }

            if (completedMilestones) {
                const currentValue = parseInt(completedMilestones.textContent);
                const newValue = currentValue + Math.floor(Math.random() * 3);
                animateNumber(completedMilestones, newValue);
            }

            if (riskProjects) {
                const currentValue = parseInt(riskProjects.textContent);
                const newValue = Math.max(0, Math.min(8, currentValue + (Math.random() - 0.7)));
                animateNumber(riskProjects, Math.round(newValue));
            }
        }

        function animateNumber(element, targetValue) {
            const currentValue = parseFloat(element.textContent);
            const difference = targetValue - currentValue;
            const steps = 20;
            const stepValue = difference / steps;
            let currentStep = 0;

            const interval = setInterval(() => {
                currentStep++;
                const newValue = currentValue + (stepValue * currentStep);

                if (currentStep >= steps) {
                    element.textContent = targetValue;
                    clearInterval(interval);
                } else {
                    element.textContent = typeof targetValue === 'string' ?
                        newValue.toFixed(1) : Math.round(newValue);
                }
            }, 50);
        }

        function addLoadingAnimations() {
            // 为卡片添加加载动画
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 80);
            });

            // 为指标卡片添加特殊动画
            const metricCards = document.querySelectorAll('.metric-card');
            metricCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'scale(0.9) translateY(20px)';
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1) translateY(0)';
                }, index * 100 + 200);
            });

            // 为指标值添加数字滚动动画
            const metricValues = document.querySelectorAll('.metric-value span');
            metricValues.forEach(metric => {
                const targetValue = parseFloat(metric.textContent);
                let currentValue = 0;
                const increment = targetValue / 30;

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= targetValue) {
                        currentValue = targetValue;
                        clearInterval(timer);
                    }

                    if (metric.id === 'completion-rate') {
                        metric.textContent = currentValue.toFixed(1);
                    } else {
                        metric.textContent = Math.round(currentValue);
                    }
                }, 50);
            });

            // 为图表容器添加渐入动画
            setTimeout(() => {
                const chartContainers = document.querySelectorAll('.chart-container, .flow-chart-container');
                chartContainers.forEach((container, index) => {
                    container.style.opacity = '0';
                    container.style.transform = 'scale(0.95)';
                    container.style.transition = 'all 0.8s ease';

                    setTimeout(() => {
                        container.style.opacity = '1';
                        container.style.transform = 'scale(1)';
                    }, index * 200);
                });
            }, 1000);
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey) {
                switch(event.key) {
                    case '1':
                        event.preventDefault();
                        switchTab('overview');
                        break;
                    case '2':
                        event.preventDefault();
                        switchTab('milestones');
                        break;
                    case '3':
                        event.preventDefault();
                        switchTab('trends');
                        break;
                    case '4':
                        event.preventDefault();
                        switchTab('analytics');
                        break;
                    case '5':
                        event.preventDefault();
                        switchTab('3d-view');
                        break;
                    case 'r':
                        event.preventDefault();
                        refreshCurrentTab();
                        break;
                }
            }
        });

        function switchTab(tabName) {
            const tab = document.querySelector(`[data-tab="${tabName}"]`);
            if (tab) {
                tab.click();
            }
        }

        function refreshCurrentTab() {
            const activeTab = document.querySelector('.nav-tab.active');
            if (activeTab) {
                const tabName = activeTab.getAttribute('data-tab');
                switch(tabName) {
                    case 'overview':
                        initOverviewCharts();
                        break;
                    case 'milestones':
                        initMilestonesCharts();
                        break;
                    case 'trends':
                        initTrendsCharts();
                        break;
                    case 'analytics':
                        initAnalyticsCharts();
                        break;
                    case '3d-view':
                        init3DScene();
                        break;
                }
            }
        }

        // 添加全屏功能
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }





        // 数据挖掘控制面板初始化
        function initDataMiningControls() {
            // 初始化滑块值显示
            updateSliderValues();

            // 模拟实时数据更新
            setInterval(updateAnalysisResults, 3000);
        }

        function updateSliderValues() {
            const timeRange = document.querySelector('input[onchange="setTimeRange(this.value)"]');
            const density = document.querySelector('input[onchange="setDataDensity(this.value)"]');
            const threshold = document.querySelector('input[onchange="setThreshold(this.value)"]');

            if (timeRange) {
                document.getElementById('time-range-value').textContent = timeRange.value;
            }
            if (density) {
                document.getElementById('density-value').textContent = density.value;
            }
            if (threshold) {
                document.getElementById('threshold-value').textContent = threshold.value;
            }
        }

        function updateAnalysisResults() {
            // 模拟动态分析结果
            const dataPoints = Math.floor(Math.random() * 1000 + 2000);
            const anomalies = Math.floor(Math.random() * 20 + 5);
            const correlation = (Math.random() * 0.3 + 0.7).toFixed(3);
            const accuracy = (Math.random() * 10 + 90).toFixed(1);

            const elements = {
                'data-points': dataPoints.toLocaleString(),
                'anomalies': `${anomalies}个异常`,
                'correlation': correlation,
                'accuracy': `${accuracy}%`
            };

            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) element.textContent = value;
            });
        }

        // 控制面板交互函数
        function setXDimension(value) {
            console.log('设置X轴维度:', value);
        }

        function setYDimension(value) {
            console.log('设置Y轴维度:', value);
        }

        function setZDimension(value) {
            console.log('设置Z轴维度:', value);
        }

        function setTimeRange(value) {
            document.getElementById('time-range-value').textContent = value;
            console.log('设置时间范围:', value + '个月');
        }

        function setDataDensity(value) {
            document.getElementById('density-value').textContent = value;
            console.log('设置数据密度:', value + '%');
        }

        function setThreshold(value) {
            document.getElementById('threshold-value').textContent = value;
            console.log('设置阈值:', value + '%');
        }

        function rotate3DCube() {
            console.log('旋转3D立方体');
        }

        function analyze3DData() {
            console.log('执行深度分析');
            updateAnalysisResults();
        }
        // 3D数据立方体
        function init3DDataCube() {
            const container = document.getElementById('datacube-3d');
            if (!container) return;

            container.innerHTML = '';

            try {
                // 创建3D数据挖掘可视化的Canvas
                const canvas = document.createElement('canvas');
                canvas.width = container.clientWidth || 800;
                canvas.height = 600;
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                container.appendChild(canvas);

                const ctx = canvas.getContext('2d');

                // 3D数据挖掘立方体数据结构
                const dataCubes = [
                    // 主数据立方体层
                    {x: 200, y: 150, z: 0, size: 60, value: 95, color: '#00ff88', label: '核心数据', type: 'primary'},
                    {x: 350, y: 150, z: 30, size: 45, value: 87, color: '#0066cc', label: '业务数据', type: 'business'},
                    {x: 500, y: 150, z: -20, size: 50, value: 92, color: '#ff6600', label: '性能数据', type: 'performance'},

                    // 分析层数据立方体
                    {x: 150, y: 250, z: 40, size: 35, value: 78, color: '#ff00ff', label: '趋势分析', type: 'trend'},
                    {x: 275, y: 250, z: -10, size: 40, value: 85, color: '#ffff00', label: '预测模型', type: 'prediction'},
                    {x: 400, y: 250, z: 25, size: 38, value: 90, color: '#00ffff', label: '风险评估', type: 'risk'},
                    {x: 525, y: 250, z: -15, size: 42, value: 88, color: '#ff8800', label: '质量控制', type: 'quality'},

                    // 深度挖掘层
                    {x: 200, y: 350, z: 20, size: 30, value: 82, color: '#8800ff', label: '关联规则', type: 'association'},
                    {x: 320, y: 350, z: -25, size: 32, value: 79, color: '#ff0088', label: '聚类分析', type: 'clustering'},
                    {x: 440, y: 350, z: 35, size: 28, value: 86, color: '#00ff44', label: '异常检测', type: 'anomaly'},

                    // 智能决策层
                    {x: 300, y: 450, z: 0, size: 25, value: 91, color: '#4400ff', label: 'AI决策', type: 'ai'},
                    {x: 380, y: 450, z: 15, size: 27, value: 89, color: '#ff4400', label: '优化建议', type: 'optimization'}
                ];

                // 数据流连接线
                const dataConnections = [
                    {from: 0, to: 3}, {from: 0, to: 4}, {from: 1, to: 4}, {from: 1, to: 5},
                    {from: 2, to: 5}, {from: 2, to: 6}, {from: 3, to: 7}, {from: 4, to: 8},
                    {from: 5, to: 9}, {from: 6, to: 9}, {from: 7, to: 10}, {from: 8, to: 10},
                    {from: 9, to: 11}, {from: 10, to: 11}
                ];

                let rotation = 0;
                let exploded = false;
                let animationPhase = 0;

                function render3DCube() {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    const centerX = canvas.width / 2;
                    const centerY = canvas.height / 2;

                    rotation += 0.008;
                    animationPhase += 0.02;

                    // 绘制数据流连接线
                    ctx.strokeStyle = 'rgba(0, 255, 136, 0.4)';
                    ctx.lineWidth = 2;
                    dataConnections.forEach(conn => {
                        const cube1 = dataCubes[conn.from];
                        const cube2 = dataCubes[conn.to];

                        const explosionFactor = exploded ? 1.8 : 1;
                        const x1 = centerX + (cube1.x - centerX) * Math.cos(rotation) * explosionFactor - cube1.z * Math.sin(rotation);
                        const y1 = centerY + (cube1.y - centerY) * explosionFactor;
                        const x2 = centerX + (cube2.x - centerX) * Math.cos(rotation) * explosionFactor - cube2.z * Math.sin(rotation);
                        const y2 = centerY + (cube2.y - centerY) * explosionFactor;

                        // 动态数据流效果
                        const flowProgress = (Math.sin(animationPhase + conn.from) + 1) / 2;
                        const flowX = x1 + (x2 - x1) * flowProgress;
                        const flowY = y1 + (y2 - y1) * flowProgress;

                        ctx.beginPath();
                        ctx.moveTo(x1, y1);
                        ctx.lineTo(x2, y2);
                        ctx.stroke();

                        // 数据流动点
                        ctx.fillStyle = '#00ff88';
                        ctx.beginPath();
                        ctx.arc(flowX, flowY, 3, 0, Math.PI * 2);
                        ctx.fill();
                    });

                    // 绘制3D数据立方体
                    dataCubes.forEach((cube, index) => {
                        const explosionFactor = exploded ? 1.8 : 1;
                        const x = centerX + (cube.x - centerX) * Math.cos(rotation) * explosionFactor - cube.z * Math.sin(rotation);
                        const y = centerY + (cube.y - centerY) * explosionFactor;
                        const size = cube.size + Math.sin(animationPhase + index) * 3; // 呼吸效果
                        const depth = 8;

                        // 3D立方体绘制
                        // 顶面
                        ctx.fillStyle = cube.color;
                        ctx.fillRect(x - size/2, y - size/2 - depth, size, size);

                        // 右侧面
                        ctx.fillStyle = adjustBrightness(cube.color, -20);
                        ctx.beginPath();
                        ctx.moveTo(x + size/2, y - size/2 - depth);
                        ctx.lineTo(x + size/2 + depth, y - size/2);
                        ctx.lineTo(x + size/2 + depth, y + size/2);
                        ctx.lineTo(x + size/2, y + size/2 - depth);
                        ctx.closePath();
                        ctx.fill();

                        // 正面
                        ctx.fillStyle = adjustBrightness(cube.color, -10);
                        ctx.fillRect(x - size/2, y - size/2, size, size - depth);

                        // 立方体边框
                        ctx.strokeStyle = '#ffffff';
                        ctx.lineWidth = 1;
                        ctx.strokeRect(x - size/2, y - size/2, size, size - depth);

                        // 数据标签
                        ctx.fillStyle = '#ffffff';
                        ctx.font = 'bold 11px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(cube.value + '%', x, y - 5);

                        // 类型标签
                        ctx.font = '9px Arial';
                        ctx.fillText(cube.label, x, y + size/2 + 15);

                        // 数据类型指示器
                        const indicatorSize = 6;
                        ctx.fillStyle = cube.color;
                        ctx.fillRect(x - indicatorSize/2, y + size/2 + 20, indicatorSize, indicatorSize);
                    });

                    // 绘制坐标轴
                    drawCoordinateAxes(ctx, centerX, centerY, rotation);

                    // 绘制数据统计信息
                    drawDataStatistics(ctx);

                    requestAnimationFrame(render3DCube);
                }

                function drawCoordinateAxes(ctx, centerX, centerY, rotation) {
                    const axisLength = 80;

                    // X轴
                    ctx.strokeStyle = '#ff0000';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(centerX - axisLength * Math.cos(rotation), centerY);
                    ctx.lineTo(centerX + axisLength * Math.cos(rotation), centerY);
                    ctx.stroke();

                    // Y轴
                    ctx.strokeStyle = '#00ff00';
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY - axisLength);
                    ctx.lineTo(centerX, centerY + axisLength);
                    ctx.stroke();

                    // Z轴
                    ctx.strokeStyle = '#0000ff';
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(centerX - axisLength * Math.sin(rotation), centerY - axisLength * 0.5);
                    ctx.stroke();
                }

                function drawDataStatistics(ctx) {
                    // 实时数据统计显示
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                    ctx.fillRect(10, 10, 200, 80);

                    ctx.fillStyle = '#00ff88';
                    ctx.font = 'bold 12px Arial';
                    ctx.textAlign = 'left';
                    ctx.fillText('实时数据挖掘状态', 20, 30);

                    ctx.fillStyle = '#ffffff';
                    ctx.font = '10px Arial';
                    ctx.fillText('活跃数据立方体: ' + dataCubes.length, 20, 45);
                    ctx.fillText('数据流连接: ' + dataConnections.length, 20, 60);
                    ctx.fillText('挖掘深度: 4层', 20, 75);
                }

                render3DCube();

                // 爆炸效果函数
                window.explode3DCube = function() {
                    exploded = !exploded;
                };

            } catch (error) {
                console.log('3D数据立方体初始化失败，使用HTML替代方案:', error);
                create3DDataCubeFallback(container);
            }
        }

        function create3DDataCubeFallback(container) {
            const cubeData = [
                {label: '核心数据挖掘', value: 95, color: '#00ff88', desc: '主要业务数据分析', layer: 1},
                {label: '业务数据处理', value: 87, color: '#0066cc', desc: '业务流程数据', layer: 1},
                {label: '性能数据监控', value: 92, color: '#ff6600', desc: '系统性能指标', layer: 1},
                {label: '趋势预测模型', value: 78, color: '#ff00ff', desc: '基于历史数据预测', layer: 2},
                {label: '预测算法优化', value: 85, color: '#ffff00', desc: '机器学习模型', layer: 2},
                {label: '风险评估体系', value: 90, color: '#00ffff', desc: '多维度风险分析', layer: 2},
                {label: '质量控制系统', value: 88, color: '#ff8800', desc: '质量保证机制', layer: 2},
                {label: '关联规则挖掘', value: 82, color: '#8800ff', desc: '数据关联性分析', layer: 3},
                {label: '聚类分析算法', value: 79, color: '#ff0088', desc: '数据分组分析', layer: 3},
                {label: '异常检测系统', value: 86, color: '#00ff44', desc: '实时异常监控', layer: 3},
                {label: 'AI智能决策', value: 91, color: '#4400ff', desc: '人工智能决策支持', layer: 4},
                {label: '优化建议引擎', value: 89, color: '#ff4400', desc: '智能优化建议', layer: 4}
            ];

            let html = '<div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 12px; padding: 15px; height: 100%; overflow-y: auto;">';
            cubeData.forEach((item, index) => {
                const layerColors = {
                    1: 'rgba(0, 255, 136, 0.1)',
                    2: 'rgba(0, 102, 204, 0.1)',
                    3: 'rgba(255, 102, 0, 0.1)',
                    4: 'rgba(255, 0, 255, 0.1)'
                };

                html += `
                    <div style="
                        background: linear-gradient(135deg, ${item.color}15, ${layerColors[item.layer]});
                        border: 2px solid ${item.color};
                        border-radius: 8px;
                        padding: 12px;
                        text-align: center;
                        color: #ffffff;
                        position: relative;
                        overflow: hidden;
                        transition: all 0.3s ease;
                        cursor: pointer;
                        min-height: 120px;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                    "
                    onmouseover="this.style.transform='scale(1.05) translateY(-2px)'; this.style.boxShadow='0 8px 25px ${item.color}40';"
                    onmouseout="this.style.transform='scale(1) translateY(0)'; this.style.boxShadow='none';">

                        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, ${item.color}, transparent);"></div>

                        <div style="position: absolute; top: 5px; right: 8px; font-size: 8px; color: ${item.color}; font-weight: bold;">L${item.layer}</div>

                        <div>
                            <div style="font-size: 20px; font-weight: bold; color: ${item.color}; margin-bottom: 6px;">${item.value}%</div>
                            <div style="font-size: 10px; font-weight: 600; margin-bottom: 6px; line-height: 1.2;">${item.label}</div>
                            <div style="font-size: 8px; color: rgba(255,255,255,0.7); margin-bottom: 8px; line-height: 1.1;">${item.desc}</div>
                        </div>

                        <div>
                            <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.2); border-radius: 2px; overflow: hidden; margin-bottom: 4px;">
                                <div style="width: ${item.value}%; height: 100%; background: linear-gradient(90deg, ${item.color}, ${item.color}80); border-radius: 2px; transition: width 0.5s ease;"></div>
                            </div>
                            <div style="font-size: 7px; color: rgba(255,255,255,0.6);">数据立方体 #${index + 1}</div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            container.innerHTML = html;
        }

        // 颜色亮度调整函数
        function adjustBrightness(color, percent) {
            // 将十六进制颜色转换为RGB
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);

            // 调整亮度
            const newR = Math.max(0, Math.min(255, r + (r * percent / 100)));
            const newG = Math.max(0, Math.min(255, g + (g * percent / 100)));
            const newB = Math.max(0, Math.min(255, b + (b * percent / 100)));

            // 转换回十六进制
            return '#' +
                Math.round(newR).toString(16).padStart(2, '0') +
                Math.round(newG).toString(16).padStart(2, '0') +
                Math.round(newB).toString(16).padStart(2, '0');
        }

        // 3D控制函数

        function change3DCubeData(dataType) {
            console.log('切换数据立方体类型:', dataType);
            // 这里可以根据数据类型改变立方体的颜色和排列
        }

        function setView(viewType) {
            console.log('设置视角:', viewType);
        }

        function change3DMode(mode) {
            console.log('切换3D模式:', mode);
        }

        function setRenderQuality(quality) {
            console.log('设置渲染质量:', quality);
            // 更新渲染指标显示
            updateRenderMetrics();
        }

        function setAnimationSpeed(speed) {
            console.log('设置动画速度:', speed);
        }

        function setLightIntensity(intensity) {
            console.log('设置光照强度:', intensity);
        }

        function toggleAutoRotate(enabled) {
            autoRotateEnabled = enabled;
        }

        function toggleShadows(enabled) {
            console.log('切换阴影:', enabled);
        }

        function toggleParticles(enabled) {
            console.log('切换粒子效果:', enabled);
        }

        function updateRenderMetrics() {
            // 模拟更新渲染指标
            try {
                const fpsEl = document.getElementById('fps-display');
                const trianglesEl = document.getElementById('triangles-display');
                const memoryEl = document.getElementById('memory-display');
                const objectsEl = document.getElementById('objects-display');
                const lightsEl = document.getElementById('lights-display');
                const renderTimeEl = document.getElementById('render-time');

                if (fpsEl) fpsEl.textContent = Math.floor(Math.random() * 10 + 55);
                if (trianglesEl) trianglesEl.textContent = (Math.random() * 2 + 1.5).toFixed(1) + 'K';
                if (memoryEl) memoryEl.textContent = Math.floor(Math.random() * 50 + 100) + 'MB';
                if (objectsEl) objectsEl.textContent = Math.floor(Math.random() * 5 + 12);
                if (lightsEl) lightsEl.textContent = Math.floor(Math.random() * 3 + 6);
                if (renderTimeEl) renderTimeEl.textContent = Math.floor(Math.random() * 8 + 12) + 'ms';
            } catch (error) {
                console.log('更新渲染指标失败:', error);
            }
        }

        // 页面卸载时清理3D资源
        window.addEventListener('beforeunload', () => {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            if (renderer) {
                renderer.dispose();
            }
        });
    </script>
</body>
</html>
